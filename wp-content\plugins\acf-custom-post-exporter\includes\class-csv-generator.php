<?php
/**
 * CSV Generator Class
 * 
 * Handles CSV file generation
 */

if (!defined('ABSPATH')) {
    exit;
}

class ACF_CPT_CSV_Generator {
    
    /**
     * Generate CSV file from data
     * 
     * @param array $data
     * @param string $post_type
     * @return array
     */
    public function generate_csv($data, $post_type) {
        try {
            // Create export directory if it doesn't exist
            $upload_dir = wp_upload_dir();
            $export_dir = $upload_dir['basedir'] . '/acf-cpt-exports/';
            
            if (!file_exists($export_dir)) {
                wp_mkdir_p($export_dir);
            }
            
            // Generate filename
            $filename = $this->generate_filename($post_type);
            $file_path = $export_dir . $filename;
            
            // Open file for writing
            $file_handle = fopen($file_path, 'w');
            
            if (!$file_handle) {
                return array(
                    'success' => false,
                    'message' => __('Could not create export file', 'acf-custom-post-exporter')
                );
            }
            
            // Add BOM for UTF-8
            fwrite($file_handle, "\xEF\xBB\xBF");
            
            // Write data to CSV
            foreach ($data as $row) {
                fputcsv($file_handle, $row);
            }
            
            fclose($file_handle);
            
            // Generate download URL
            $download_url = add_query_arg(array(
                'action' => 'acf_cpt_download_file',
                'file' => $filename,
                'nonce' => wp_create_nonce('acf_cpt_download_nonce')
            ), admin_url('admin-ajax.php'));
            
            return array(
                'success' => true,
                'filename' => $filename,
                'file_path' => $file_path,
                'download_url' => $download_url,
                'file_size' => filesize($file_path)
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => __('Error generating CSV: ', 'acf-custom-post-exporter') . $e->getMessage()
            );
        }
    }
    
    /**
     * Generate unique filename
     * 
     * @param string $post_type
     * @return string
     */
    private function generate_filename($post_type) {
        $timestamp = date('Y-m-d_H-i-s');
        $filename = sanitize_file_name($post_type . '_export_' . $timestamp . '.csv');
        return $filename;
    }
    
    /**
     * Validate CSV data
     * 
     * @param array $data
     * @return bool
     */
    public function validate_csv_data($data) {
        if (!is_array($data) || empty($data)) {
            return false;
        }
        
        // Check if first row exists (headers)
        if (!isset($data[0]) || !is_array($data[0])) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Get file size in human readable format
     * 
     * @param int $bytes
     * @return string
     */
    public function format_file_size($bytes) {
        $units = array('B', 'KB', 'MB', 'GB');
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * Clean up old export files
     * 
     * @param int $max_age_hours
     */
    public function cleanup_old_files($max_age_hours = 24) {
        $upload_dir = wp_upload_dir();
        $export_dir = $upload_dir['basedir'] . '/acf-cpt-exports/';
        
        if (!is_dir($export_dir)) {
            return;
        }
        
        $files = glob($export_dir . '*.csv');
        $max_age_seconds = $max_age_hours * 3600;
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $file_age = time() - filemtime($file);
                if ($file_age > $max_age_seconds) {
                    unlink($file);
                }
            }
        }
    }
}
