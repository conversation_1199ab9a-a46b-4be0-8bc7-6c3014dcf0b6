# ACF Custom Post Type Exporter

A comprehensive WordPress plugin for exporting ACF (Advanced Custom Fields) field values from any custom post type to CSV format.

## Features

- **Universal Post Type Support**: Export from any custom post type including WooCommerce products, custom post types, and standard WordPress posts/pages
- **ACF Field Detection**: Automatically detects and lists all ACF fields associated with the selected post type
- **Standard Field Support**: Includes standard WordPress fields like title, content, excerpt, featured image, taxonomies, etc.
- **Flexible Field Selection**: Choose exactly which fields to include in your export
- **User-Friendly Interface**: Step-by-step wizard interface for easy configuration
- **Export Options**: Set limits and offsets for large datasets
- **Secure Downloads**: Temporary file generation with automatic cleanup
- **Responsive Design**: Works on desktop and mobile devices

## Supported Field Types

### ACF Fields
- Text, Textarea, Number
- Email, URL, Password
- Select, Checkbox, Radio, True/False
- Date Picker, Date Time Picker, Time Picker
- Color Picker, Range
- Image, File, Gallery
- Post Object, Relationship
- Taxonomy, User
- WYSIWYG Editor
- oEmbed, Google Map
- Repeater fields (basic support)
- Group fields (basic support)

### Standard WordPress Fields
- Post ID, Title, Content, Excerpt
- Post Status, Date Published, Date Modified
- Featured Image with URL and alt text
- Author information
- Taxonomies (categories, tags, custom taxonomies)
- Permalink

## Installation

1. Upload the plugin files to `/wp-content/plugins/acf-custom-post-exporter/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Navigate to Tools > ACF Post Exporter to start using the plugin

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- Advanced Custom Fields (ACF) plugin (free or pro version)

## Usage

### Step 1: Select Post Type
1. Go to Tools > ACF Post Exporter in your WordPress admin
2. Select the post type you want to export from the dropdown
3. View the post count for the selected post type
4. Click "Load Fields" to proceed

### Step 2: Select Fields
1. Choose from Standard Fields (WordPress default fields)
2. Choose from ACF Fields (organized by field groups)
3. Use "Select All" or "Deselect All" for quick selection
4. Review your field selection

### Step 3: Export Options
1. Set a limit for the number of records (optional)
2. Set an offset to skip records from the beginning (optional)
3. Click "Start Export" to begin the process

### Step 4: Download
1. Wait for the export to complete
2. Download the generated CSV file
3. Files are automatically cleaned up after 24 hours

## Export Format

The plugin generates CSV files with the following characteristics:
- UTF-8 encoding with BOM for proper character support
- Comma-separated values
- Headers in the first row
- Properly escaped special characters
- Array values converted to comma-separated strings
- Object values converted to readable format

## Field Value Formatting

### Images and Files
- Image fields export as: `URL|Alt Text`
- File fields export as: `URL|Filename`
- Gallery fields export as comma-separated URLs

### Relationships
- Post Object fields export as post titles
- Relationship fields export as comma-separated post titles
- User fields export as display names
- Taxonomy fields export as comma-separated term names

### Complex Fields
- Array values are converted to comma-separated strings
- Object values are converted to JSON or readable format
- Boolean values are converted to "Yes"/"No"

## Security Features

- Nonce verification for all AJAX requests
- User capability checks (requires 'manage_options')
- Sanitized input validation
- Secure file handling with temporary storage
- Automatic file cleanup

## Performance Considerations

- Large exports are processed efficiently
- Memory usage optimized for large datasets
- Temporary file storage prevents browser timeouts
- Progress indication for user feedback

## Troubleshooting

### Common Issues

**"ACF missing" error**
- Ensure Advanced Custom Fields plugin is installed and activated

**"No fields found" message**
- Verify that ACF field groups are assigned to the selected post type
- Check that field groups are active

**Export fails or times out**
- Try reducing the number of records with the limit option
- Check server memory limits and execution time
- Verify write permissions for the uploads directory

**Download link not working**
- Check that the file hasn't expired (24-hour limit)
- Verify server permissions for file access
- Try regenerating the export

### File Permissions
Ensure your WordPress uploads directory is writable:
```
wp-content/uploads/ (755 or 775)
```

## Hooks and Filters

The plugin provides several hooks for developers:

### Filters
- `acf_cpt_exporter_post_types` - Modify available post types
- `acf_cpt_exporter_fields` - Modify field list
- `acf_cpt_exporter_export_data` - Modify export data before CSV generation
- `acf_cpt_exporter_filename` - Customize export filename

### Actions
- `acf_cpt_exporter_before_export` - Fired before export starts
- `acf_cpt_exporter_after_export` - Fired after export completes

## Support

For support and feature requests, please contact the development team or create an issue in the project repository.

## Changelog

### Version 1.0.0
- Initial release
- Support for all major ACF field types
- Step-by-step export wizard
- Responsive admin interface
- Secure file handling
- Automatic cleanup functionality

## License

This plugin is licensed under the GPL v2 or later.

## Credits

Developed by the Hisense Development Team for efficient ACF data management and export capabilities.
