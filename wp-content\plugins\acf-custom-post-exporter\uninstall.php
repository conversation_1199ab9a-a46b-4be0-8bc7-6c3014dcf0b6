<?php
/**
 * Uninstall script for ACF Custom Post Type Exporter
 * 
 * This file is executed when the plugin is uninstalled
 */

// If uninstall not called from WordPress, then exit
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Clean up export files
$upload_dir = wp_upload_dir();
$export_dir = $upload_dir['basedir'] . '/acf-cpt-exports/';

if (is_dir($export_dir)) {
    $files = glob($export_dir . '*');
    foreach ($files as $file) {
        if (is_file($file)) {
            unlink($file);
        }
    }
    rmdir($export_dir);
}

// Clean up options
delete_option('acf_cpt_exporter_version');

// Clean up transients
global $wpdb;
$wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_acf_cpt_export_%'");
$wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_acf_cpt_export_%'");
