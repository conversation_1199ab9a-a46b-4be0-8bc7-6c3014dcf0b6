<?php
/**
 * Plugin Name: WooCommerce ACF Product Exporter
 * Plugin URI: https://yourwebsite.com
 * Description: Export WooCommerce products including ACF fields, ACF taxonomies, and all default WooCommerce values in CSV format for import to another WooCommerce site.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://yourwebsite.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: wc-acf-product-exporter
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.3
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WC_ACF_PRODUCT_EXPORTER_VERSION', '1.0.0');
define('WC_ACF_PRODUCT_EXPORTER_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WC_ACF_PRODUCT_EXPORTER_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WC_ACF_PRODUCT_EXPORTER_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main plugin class
 */
class WC_ACF_Product_Exporter {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }
        
        // Load text domain
        load_plugin_textdomain('wc-acf-product-exporter', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Include required files
        $this->includes();
        
        // Initialize admin
        if (is_admin()) {
            $this->init_admin();
        }
        
        // Initialize AJAX handlers
        $this->init_ajax();
    }
    
    /**
     * Include required files
     */
    private function includes() {
        require_once WC_ACF_PRODUCT_EXPORTER_PLUGIN_DIR . 'includes/class-product-data-collector.php';
        require_once WC_ACF_PRODUCT_EXPORTER_PLUGIN_DIR . 'includes/class-csv-exporter.php';
        require_once WC_ACF_PRODUCT_EXPORTER_PLUGIN_DIR . 'includes/class-acf-handler.php';
        
        if (is_admin()) {
            require_once WC_ACF_PRODUCT_EXPORTER_PLUGIN_DIR . 'admin/class-admin.php';
        }
    }
    
    /**
     * Initialize admin
     */
    private function init_admin() {
        new WC_ACF_Product_Exporter_Admin();
    }
    
    /**
     * Initialize AJAX handlers
     */
    private function init_ajax() {
        add_action('wp_ajax_wc_acf_export_products', array($this, 'ajax_export_products'));
        add_action('wp_ajax_wc_acf_get_export_progress', array($this, 'ajax_get_export_progress'));
        add_action('wp_ajax_wc_acf_test_export', array($this, 'ajax_test_export'));
    }
    
    /**
     * AJAX handler for product export
     */
    public function ajax_export_products() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wc_acf_export_nonce')) {
            wp_send_json_error(__('Security check failed', 'wc-acf-product-exporter'));
        }

        // Check user capabilities
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Insufficient permissions', 'wc-acf-product-exporter'));
        }

        try {
            $exporter = new WC_ACF_Product_CSV_Exporter();
            $result = $exporter->export($_POST);

            if ($result['success']) {
                wp_send_json_success($result);
            } else {
                wp_send_json_error($result);
            }
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Export failed: ', 'wc-acf-product-exporter') . $e->getMessage()
            ));
        }
    }
    
    /**
     * AJAX handler for export progress
     */
    public function ajax_get_export_progress() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wc_acf_export_nonce')) {
            wp_die(__('Security check failed', 'wc-acf-product-exporter'));
        }

        $progress = get_transient('wc_acf_export_progress_' . get_current_user_id());
        wp_send_json($progress ? $progress : array('status' => 'not_found'));
    }

    /**
     * Test export function for debugging
     */
    public function ajax_test_export() {
        // Create a simple test CSV file
        $upload_dir = wp_upload_dir();
        $export_dir = $upload_dir['basedir'] . '/wc-acf-exports/';

        if (!file_exists($export_dir)) {
            wp_mkdir_p($export_dir);
        }

        $filename = 'test-export-' . date('Y-m-d-H-i-s') . '.csv';
        $file_path = $export_dir . $filename;

        $test_data = "ID,Name,SKU,Price\n1,Test Product,TEST001,29.99\n";
        file_put_contents($file_path, $test_data);

        $download_url = add_query_arg(array(
            'action' => 'download_export',
            'file' => $filename,
            'nonce' => wp_create_nonce('wc_acf_download_nonce')
        ), admin_url('admin.php'));

        wp_send_json_success(array(
            'message' => 'Test export created',
            'filename' => $filename,
            'download_url' => $download_url,
            'file_path' => $file_path,
            'file_exists' => file_exists($file_path)
        ));
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create necessary database tables or options if needed
        update_option('wc_acf_product_exporter_version', WC_ACF_PRODUCT_EXPORTER_VERSION);
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up temporary files and transients
        $this->cleanup_temp_files();
    }
    
    /**
     * Clean up temporary files
     */
    private function cleanup_temp_files() {
        $upload_dir = wp_upload_dir();
        $export_dir = $upload_dir['basedir'] . '/wc-acf-exports/';
        
        if (is_dir($export_dir)) {
            $files = glob($export_dir . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
    }
    
    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        echo '<div class="notice notice-error"><p>';
        echo __('WooCommerce ACF Product Exporter requires WooCommerce to be installed and active.', 'wc-acf-product-exporter');
        echo '</p></div>';
    }
}

// Initialize the plugin
WC_ACF_Product_Exporter::get_instance();
