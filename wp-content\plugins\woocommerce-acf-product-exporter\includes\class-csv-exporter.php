<?php
/**
 * CSV Exporter class for WooCommerce ACF Product Exporter
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WC_ACF_Product_CSV_Exporter {
    
    private $data_collector;
    private $export_options;
    private $progress_key;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->data_collector = new WC_ACF_Product_Data_Collector();
        $this->progress_key = 'wc_acf_export_progress_' . get_current_user_id();
    }
    
    /**
     * Export products to CSV
     */
    public function export($options = array()) {
        $this->export_options = wp_parse_args($options, array(
            'product_status' => array('publish'),
            'product_types' => array('simple', 'variable'),
            'include_variations' => true,
            'include_acf_fields' => true,
            'include_acf_taxonomies' => true,
            'batch_size' => 50
        ));
        
        try {
            // Initialize progress tracking
            $this->update_progress(0, __('Starting export...', 'wc-acf-product-exporter'));
            
            // Get products to export
            $product_args = array(
                'status' => $this->export_options['product_status'],
                'type' => $this->export_options['product_types'],
                'limit' => -1,
                'return' => 'ids'
            );
            
            $product_ids = $this->data_collector->get_products($product_args);
            
            if (empty($product_ids)) {
                return array(
                    'success' => false,
                    'message' => __('No products found to export.', 'wc-acf-product-exporter')
                );
            }
            
            $total_products = count($product_ids);
            $this->update_progress(5, sprintf(__('Found %d products to export...', 'wc-acf-product-exporter'), $total_products));
            
            // Create export directory
            $upload_dir = wp_upload_dir();
            $export_dir = $upload_dir['basedir'] . '/wc-acf-exports/';

            if (!file_exists($export_dir)) {
                if (!wp_mkdir_p($export_dir)) {
                    return array(
                        'success' => false,
                        'message' => __('Could not create export directory.', 'wc-acf-product-exporter')
                    );
                }
            }
            
            // Generate filename
            $filename = 'wc-acf-products-export-' . date('Y-m-d-H-i-s') . '.csv';
            $file_path = $export_dir . $filename;
            
            // Open file for writing
            $file_handle = fopen($file_path, 'w');
            
            if (!$file_handle) {
                return array(
                    'success' => false,
                    'message' => __('Could not create export file.', 'wc-acf-product-exporter')
                );
            }
            
            // Set CSV headers
            $headers_written = false;
            $processed_count = 0;
            $batch_size = intval($this->export_options['batch_size']);
            
            // Process products in batches
            $batches = array_chunk($product_ids, $batch_size);
            $total_batches = count($batches);
            
            foreach ($batches as $batch_index => $batch_product_ids) {
                $batch_data = array();
                
                foreach ($batch_product_ids as $product_id) {
                    $product_data = $this->data_collector->get_product_data($product_id, $this->export_options);
                    
                    if ($product_data) {
                        $batch_data = array_merge($batch_data, $product_data);
                    }
                    
                    $processed_count++;
                    
                    // Update progress
                    $progress = 10 + (($processed_count / $total_products) * 80);
                    $this->update_progress(
                        $progress,
                        sprintf(__('Processing product %d of %d...', 'wc-acf-product-exporter'), $processed_count, $total_products)
                    );
                }
                
                // Write batch data to CSV
                if (!empty($batch_data)) {
                    if (!$headers_written) {
                        // Write headers
                        $headers = $this->get_csv_headers($batch_data);
                        fputcsv($file_handle, $headers);
                        $headers_written = true;
                    }
                    
                    // Write data rows
                    foreach ($batch_data as $row_data) {
                        $csv_row = $this->prepare_csv_row($row_data, $headers);
                        fputcsv($file_handle, $csv_row);
                    }
                }
                
                // Clear memory
                unset($batch_data);
                
                // Update batch progress
                $batch_progress = 10 + (($batch_index + 1) / $total_batches) * 80;
                $this->update_progress(
                    $batch_progress,
                    sprintf(__('Completed batch %d of %d...', 'wc-acf-product-exporter'), $batch_index + 1, $total_batches)
                );
            }
            
            fclose($file_handle);

            // Generate import instructions
            $instructions_filename = $this->generate_import_instructions($filename);

            // Final progress update
            $this->update_progress(100, __('Export completed successfully!', 'wc-acf-product-exporter'));

            return array(
                'success' => true,
                'message' => __('Export completed successfully!', 'wc-acf-product-exporter'),
                'filename' => $filename,
                'download_url' => $this->get_download_url($filename),
                'instructions_filename' => $instructions_filename,
                'instructions_download_url' => $this->get_download_url($instructions_filename),
                'total_products' => $total_products,
                'total_rows' => $processed_count
            );
            
        } catch (Exception $e) {
            $this->update_progress(0, __('Export failed: ', 'wc-acf-product-exporter') . $e->getMessage());
            
            return array(
                'success' => false,
                'message' => __('Export failed: ', 'wc-acf-product-exporter') . $e->getMessage()
            );
        }
    }
    
    /**
     * Get CSV headers from data
     */
    private function get_csv_headers($data) {
        $headers = array();
        
        // Get all unique keys from all rows
        foreach ($data as $row) {
            $headers = array_merge($headers, array_keys($row));
        }
        
        // Remove duplicates and sort
        $headers = array_unique($headers);
        
        // Define preferred order for common fields
        $preferred_order = array(
            'ID', 'post_title', 'post_content', 'post_excerpt', 'post_status',
            'product_type', 'sku', 'regular_price', 'sale_price', 'price',
            'manage_stock', 'stock_quantity', 'stock_status', 'weight',
            'length', 'width', 'height', 'featured_image', 'product_gallery'
        );
        
        // Sort headers with preferred order first
        $ordered_headers = array();
        
        // Add preferred headers first
        foreach ($preferred_order as $preferred_header) {
            if (in_array($preferred_header, $headers)) {
                $ordered_headers[] = $preferred_header;
            }
        }
        
        // Add remaining headers
        foreach ($headers as $header) {
            if (!in_array($header, $ordered_headers)) {
                $ordered_headers[] = $header;
            }
        }
        
        return $ordered_headers;
    }
    
    /**
     * Prepare CSV row data
     */
    private function prepare_csv_row($row_data, $headers) {
        $csv_row = array();
        
        foreach ($headers as $header) {
            $value = isset($row_data[$header]) ? $row_data[$header] : '';
            
            // Clean and format value
            $value = $this->clean_csv_value($value);
            
            $csv_row[] = $value;
        }
        
        return $csv_row;
    }
    
    /**
     * Clean CSV value
     */
    private function clean_csv_value($value) {
        if (is_array($value)) {
            $value = implode(',', $value);
        }
        
        // Remove line breaks and extra whitespace
        $value = preg_replace('/\s+/', ' ', $value);
        $value = trim($value);
        
        // Escape quotes
        $value = str_replace('"', '""', $value);
        
        return $value;
    }
    
    /**
     * Update export progress
     */
    private function update_progress($percentage, $message) {
        $progress_data = array(
            'percentage' => $percentage,
            'message' => $message,
            'timestamp' => time()
        );
        
        set_transient($this->progress_key, $progress_data, 3600); // 1 hour expiry
    }
    
    /**
     * Get download URL
     */
    private function get_download_url($filename) {
        return add_query_arg(array(
            'action' => 'download_export',
            'file' => $filename,
            'nonce' => wp_create_nonce('wc_acf_download_nonce')
        ), admin_url('admin.php'));
    }
    
    /**
     * Get export progress
     */
    public function get_progress() {
        return get_transient($this->progress_key);
    }
    
    /**
     * Clear export progress
     */
    public function clear_progress() {
        delete_transient($this->progress_key);
    }

    /**
     * Generate import instructions file
     */
    public function generate_import_instructions($filename) {
        $upload_dir = wp_upload_dir();
        $export_dir = $upload_dir['basedir'] . '/wc-acf-exports/';

        $instructions_filename = str_replace('.csv', '-import-instructions.txt', $filename);
        $instructions_path = $export_dir . $instructions_filename;

        $instructions = $this->get_import_instructions_content();

        file_put_contents($instructions_path, $instructions);

        return $instructions_filename;
    }

    /**
     * Get import instructions content
     */
    private function get_import_instructions_content() {
        $instructions = "WooCommerce ACF Product Import Instructions\n";
        $instructions .= "==========================================\n\n";

        $instructions .= "BEFORE IMPORTING:\n";
        $instructions .= "-----------------\n";
        $instructions .= "1. Ensure WooCommerce is installed and active on the target site\n";
        $instructions .= "2. Install and activate Advanced Custom Fields Pro\n";
        $instructions .= "3. Import/recreate all ACF field groups used in the export\n";
        $instructions .= "4. Create any custom taxonomies that were exported\n";
        $instructions .= "5. Backup your target site database\n\n";

        $instructions .= "IMPORT PROCESS:\n";
        $instructions .= "---------------\n";
        $instructions .= "1. Go to WooCommerce > Products > Import\n";
        $instructions .= "2. Upload the exported CSV file\n";
        $instructions .= "3. Map the columns during import:\n";
        $instructions .= "   - Standard WooCommerce fields will auto-map\n";
        $instructions .= "   - ACF fields (acf_*) should be mapped to 'Meta: field_name'\n";
        $instructions .= "   - Taxonomy fields should be mapped appropriately\n";
        $instructions .= "4. Choose update existing products if needed\n";
        $instructions .= "5. Run the import\n\n";

        $instructions .= "FIELD MAPPING GUIDE:\n";
        $instructions .= "--------------------\n";
        $instructions .= "Standard Fields:\n";
        $instructions .= "- ID -> ID\n";
        $instructions .= "- post_title -> Name\n";
        $instructions .= "- post_content -> Description\n";
        $instructions .= "- post_excerpt -> Short description\n";
        $instructions .= "- sku -> SKU\n";
        $instructions .= "- regular_price -> Regular price\n";
        $instructions .= "- sale_price -> Sale price\n";
        $instructions .= "- stock_quantity -> Stock\n";
        $instructions .= "- weight -> Weight\n";
        $instructions .= "- length -> Length\n";
        $instructions .= "- width -> Width\n";
        $instructions .= "- height -> Height\n";
        $instructions .= "- featured_image -> Images\n";
        $instructions .= "- product_gallery -> Images (additional)\n\n";

        $instructions .= "ACF Fields:\n";
        $instructions .= "- acf_field_name -> Meta: field_name\n";
        $instructions .= "- Remove 'acf_' prefix when mapping\n\n";

        $instructions .= "Taxonomies:\n";
        $instructions .= "- taxonomy_product_cat -> Categories\n";
        $instructions .= "- taxonomy_product_tag -> Tags\n";
        $instructions .= "- taxonomy_custom_name -> Custom taxonomy name\n\n";

        $instructions .= "TROUBLESHOOTING:\n";
        $instructions .= "----------------\n";
        $instructions .= "- If ACF fields don't import, ensure field groups exist\n";
        $instructions .= "- Check field names match exactly (case-sensitive)\n";
        $instructions .= "- For image fields, ensure URLs are accessible\n";
        $instructions .= "- Large imports may need to be split into smaller files\n";
        $instructions .= "- Enable WordPress debug mode for detailed error messages\n\n";

        $instructions .= "POST-IMPORT:\n";
        $instructions .= "------------\n";
        $instructions .= "1. Check product data and ACF fields\n";
        $instructions .= "2. Verify images imported correctly\n";
        $instructions .= "3. Test product variations if applicable\n";
        $instructions .= "4. Update product permalinks if needed\n";
        $instructions .= "5. Clear any caching\n\n";

        $instructions .= "Generated on: " . date('Y-m-d H:i:s') . "\n";

        return $instructions;
    }
}
