<?php
/**
 * Admin Page Class
 * 
 * Handles the admin interface for the importer plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

class ACF_CPT_Importer_Admin_Page {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_management_page(
            __('ACF Custom Post Importer', 'acf-custom-post-importer'),
            __('ACF Post Importer', 'acf-custom-post-importer'),
            'manage_options',
            'acf-cpt-importer',
            array($this, 'admin_page_content')
        );
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Debug: Log the hook
        error_log('ACF Importer - Hook: ' . $hook);

        if ($hook !== 'tools_page_acf-cpt-importer') {
            return;
        }

        // Debug: Log that we're enqueueing scripts
        error_log('ACF Importer - Enqueueing scripts');

        wp_enqueue_script(
            'acf-cpt-importer-admin',
            ACF_CPT_IMPORTER_PLUGIN_URL . 'admin/js/admin.js',
            array('jquery'),
            ACF_CPT_IMPORTER_VERSION,
            true
        );
        
        wp_enqueue_style(
            'acf-cpt-importer-admin',
            ACF_CPT_IMPORTER_PLUGIN_URL . 'admin/css/admin.css',
            array(),
            ACF_CPT_IMPORTER_VERSION
        );
        
        wp_localize_script('acf-cpt-importer-admin', 'acf_cpt_importer', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('acf_cpt_import_nonce'),
            'max_file_size' => wp_max_upload_size(),
            'strings' => array(
                'loading' => __('Loading...', 'acf-custom-post-importer'),
                'uploading' => __('Uploading...', 'acf-custom-post-importer'),
                'parsing' => __('Parsing CSV...', 'acf-custom-post-importer'),
                'importing' => __('Importing data...', 'acf-custom-post-importer'),
                'select_file' => __('Please select a CSV file', 'acf-custom-post-importer'),
                'select_post_type' => __('Please select a post type', 'acf-custom-post-importer'),
                'map_required_fields' => __('Please map at least the required fields', 'acf-custom-post-importer'),
                'import_success' => __('Import completed successfully!', 'acf-custom-post-importer'),
                'import_error' => __('Import failed. Please try again.', 'acf-custom-post-importer'),
                'confirm_import' => __('Are you sure you want to start the import? This action cannot be undone.', 'acf-custom-post-importer'),
                'file_too_large' => __('File size exceeds the maximum allowed size', 'acf-custom-post-importer')
            )
        ));
    }
    
    /**
     * Admin page content
     */
    public function admin_page_content() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

            <!-- Debug button - remove after testing -->
            <div style="background: #fff; padding: 10px; margin: 10px 0; border: 1px solid #ccc;">
                <strong>Debug:</strong>
                <button type="button" onclick="debugLoadPostTypes()" class="button">Test Load Post Types</button>
                <button type="button" onclick="testAjax()" class="button">Test AJAX Direct</button>
                <span style="color: #666; font-size: 12px;">Check browser console for results</span>
            </div>

            <script>
            // Inline test function
            function testAjax() {
                console.log('Testing AJAX directly...');
                jQuery.ajax({
                    url: '<?php echo admin_url('admin-ajax.php'); ?>',
                    type: 'POST',
                    data: {
                        action: 'acf_cpt_get_post_types',
                        nonce: '<?php echo wp_create_nonce('acf_cpt_import_nonce'); ?>'
                    },
                    success: function(response) {
                        console.log('Direct AJAX success:', response);
                    },
                    error: function(xhr, status, error) {
                        console.error('Direct AJAX error:', xhr, status, error);
                    }
                });
            }

            // Simple debug function
            function debugLoadPostTypes() {
                console.log('Debug button clicked');
                if (typeof window.ACFCPTImporter !== 'undefined') {
                    window.ACFCPTImporter.loadPostTypes();
                } else if (typeof window.debugLoadPostTypes !== 'undefined') {
                    window.debugLoadPostTypes();
                } else {
                    console.error('ACFCPTImporter not loaded');
                    testAjax(); // Fallback to direct test
                }
            }
            </script>

            <div class="acf-cpt-importer-container">
                <div class="acf-cpt-importer-main">
                    
                    <!-- Step 1: Upload CSV -->
                    <div class="acf-cpt-step" id="step-1">
                        <h2><?php _e('Step 1: Upload CSV File', 'acf-custom-post-importer'); ?></h2>
                        <p><?php _e('Upload a CSV file containing the data you want to import:', 'acf-custom-post-importer'); ?></p>
                        
                        <div class="upload-area">
                            <form id="csv-upload-form" enctype="multipart/form-data">
                                <div class="upload-box">
                                    <div class="upload-icon">📁</div>
                                    <p><?php _e('Drag and drop your CSV file here, or click to browse', 'acf-custom-post-importer'); ?></p>
                                    <input type="file" id="csv-file" name="csv_file" accept=".csv" required>
                                    <button type="button" class="button" id="browse-btn"><?php _e('Browse Files', 'acf-custom-post-importer'); ?></button>
                                </div>
                                <div class="upload-info">
                                    <p><strong><?php _e('File Requirements:', 'acf-custom-post-importer'); ?></strong></p>
                                    <ul>
                                        <li><?php _e('CSV format only', 'acf-custom-post-importer'); ?></li>
                                        <li><?php printf(__('Maximum file size: %s', 'acf-custom-post-importer'), size_format(wp_max_upload_size())); ?></li>
                                        <li><?php _e('First row should contain column headers', 'acf-custom-post-importer'); ?></li>
                                        <li><?php _e('UTF-8 encoding recommended', 'acf-custom-post-importer'); ?></li>
                                    </ul>
                                </div>
                                <button type="submit" class="button button-primary" id="upload-btn" disabled>
                                    <?php _e('Upload and Parse', 'acf-custom-post-importer'); ?>
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Step 2: Select Post Type -->
                    <div class="acf-cpt-step" id="step-2" style="display: none;">
                        <h2><?php _e('Step 2: Select Post Type and Map Fields', 'acf-custom-post-importer'); ?></h2>
                        
                        <div class="post-type-selection">
                            <label for="import-post-type"><?php _e('Target Post Type:', 'acf-custom-post-importer'); ?></label>
                            <select id="import-post-type" class="regular-text">
                                <option value=""><?php _e('Select post type...', 'acf-custom-post-importer'); ?></option>
                            </select>
                        </div>
                        
                        <div id="csv-preview" class="csv-preview" style="display: none;">
                            <h3><?php _e('CSV Preview', 'acf-custom-post-importer'); ?></h3>
                            <div class="preview-info">
                                <p><strong><?php _e('Total Rows:', 'acf-custom-post-importer'); ?></strong> <span id="total-rows">0</span></p>
                                <p><strong><?php _e('Columns Found:', 'acf-custom-post-importer'); ?></strong> <span id="total-columns">0</span></p>
                            </div>
                            <div class="preview-table-container">
                                <table id="preview-table" class="wp-list-table widefat fixed striped">
                                    <thead></thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div id="field-mapping" class="field-mapping" style="display: none;">
                            <h3><?php _e('Field Mapping', 'acf-custom-post-importer'); ?></h3>
                            <p><?php _e('Map your CSV columns to WordPress/ACF fields:', 'acf-custom-post-importer'); ?></p>
                            
                            <div class="mapping-controls">
                                <button type="button" id="auto-map-btn" class="button">
                                    <?php _e('Auto Map Fields', 'acf-custom-post-importer'); ?>
                                </button>
                                <button type="button" id="clear-mapping-btn" class="button">
                                    <?php _e('Clear Mapping', 'acf-custom-post-importer'); ?>
                                </button>
                            </div>
                            
                            <div id="mapping-table-container">
                                <table id="mapping-table" class="wp-list-table widefat fixed striped">
                                    <thead>
                                        <tr>
                                            <th><?php _e('CSV Column', 'acf-custom-post-importer'); ?></th>
                                            <th><?php _e('Sample Data', 'acf-custom-post-importer'); ?></th>
                                            <th><?php _e('Map to Field', 'acf-custom-post-importer'); ?></th>
                                            <th><?php _e('Field Type', 'acf-custom-post-importer'); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step 3: Import Options -->
                    <div class="acf-cpt-step" id="step-3" style="display: none;">
                        <h2><?php _e('Step 3: Import Options', 'acf-custom-post-importer'); ?></h2>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="import-mode"><?php _e('Import Mode', 'acf-custom-post-importer'); ?></label>
                                </th>
                                <td>
                                    <select id="import-mode" class="regular-text">
                                        <option value="create"><?php _e('Create new posts only', 'acf-custom-post-importer'); ?></option>
                                        <option value="update"><?php _e('Update existing posts only', 'acf-custom-post-importer'); ?></option>
                                        <option value="create_update"><?php _e('Create new or update existing', 'acf-custom-post-importer'); ?></option>
                                    </select>
                                    <p class="description"><?php _e('Choose how to handle existing posts (requires ID column for updates)', 'acf-custom-post-importer'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="batch-size"><?php _e('Batch Size', 'acf-custom-post-importer'); ?></label>
                                </th>
                                <td>
                                    <input type="number" id="batch-size" class="regular-text" value="50" min="1" max="200">
                                    <p class="description"><?php _e('Number of records to process at once (lower for large datasets)', 'acf-custom-post-importer'); ?></p>
                                </td>
                            </tr>
                        </table>
                        
                        <div id="validation-results" class="validation-results" style="display: none;">
                            <h3><?php _e('Validation Results', 'acf-custom-post-importer'); ?></h3>
                            <div id="validation-errors" class="notice notice-error" style="display: none;">
                                <h4><?php _e('Errors Found:', 'acf-custom-post-importer'); ?></h4>
                                <ul></ul>
                            </div>
                            <div id="validation-warnings" class="notice notice-warning" style="display: none;">
                                <h4><?php _e('Warnings:', 'acf-custom-post-importer'); ?></h4>
                                <ul></ul>
                            </div>
                            <div id="import-summary" class="import-summary">
                                <p><strong><?php _e('Import Summary:', 'acf-custom-post-importer'); ?></strong></p>
                                <ul>
                                    <li><?php _e('Total records:', 'acf-custom-post-importer'); ?> <span id="summary-total">0</span></li>
                                    <li><?php _e('Estimated time:', 'acf-custom-post-importer'); ?> <span id="summary-time">-</span></li>
                                    <li><?php _e('Batch size:', 'acf-custom-post-importer'); ?> <span id="summary-batch">50</span></li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="import-actions">
                            <button type="button" id="validate-btn" class="button">
                                <?php _e('Validate Data', 'acf-custom-post-importer'); ?>
                            </button>
                            <button type="button" id="start-import-btn" class="button button-primary button-large" disabled>
                                <?php _e('Start Import', 'acf-custom-post-importer'); ?>
                            </button>
                            <button type="button" id="reset-import-btn" class="button">
                                <?php _e('Reset', 'acf-custom-post-importer'); ?>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Import Progress -->
                    <div class="acf-cpt-step" id="import-progress" style="display: none;">
                        <h2><?php _e('Import in Progress', 'acf-custom-post-importer'); ?></h2>
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%;"></div>
                            </div>
                            <div class="progress-stats">
                                <p class="progress-text"><?php _e('Preparing import...', 'acf-custom-post-importer'); ?></p>
                                <div class="progress-numbers">
                                    <span><?php _e('Processed:', 'acf-custom-post-importer'); ?> <span id="processed-count">0</span></span>
                                    <span><?php _e('Created:', 'acf-custom-post-importer'); ?> <span id="created-count">0</span></span>
                                    <span><?php _e('Updated:', 'acf-custom-post-importer'); ?> <span id="updated-count">0</span></span>
                                    <span><?php _e('Errors:', 'acf-custom-post-importer'); ?> <span id="error-count">0</span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Import Results -->
                    <div class="acf-cpt-step" id="import-results" style="display: none;">
                        <h2><?php _e('Import Complete', 'acf-custom-post-importer'); ?></h2>
                        
                        <div id="import-success" class="notice notice-success" style="display: none;">
                            <h3><?php _e('Import completed successfully!', 'acf-custom-post-importer'); ?></h3>
                            <div id="final-summary"></div>
                        </div>
                        
                        <div id="import-errors" class="notice notice-error" style="display: none;">
                            <h3><?php _e('Import completed with errors:', 'acf-custom-post-importer'); ?></h3>
                            <div id="error-details"></div>
                        </div>
                        
                        <button type="button" id="new-import-btn" class="button button-primary">
                            <?php _e('Start New Import', 'acf-custom-post-importer'); ?>
                        </button>
                    </div>
                    
                </div>
                
                <!-- Sidebar -->
                <div class="acf-cpt-importer-sidebar">
                    <div class="sidebar-box">
                        <h3><?php _e('Import Guide', 'acf-custom-post-importer'); ?></h3>
                        <ol>
                            <li><?php _e('Prepare your CSV file with proper headers', 'acf-custom-post-importer'); ?></li>
                            <li><?php _e('Upload and preview your data', 'acf-custom-post-importer'); ?></li>
                            <li><?php _e('Select target post type', 'acf-custom-post-importer'); ?></li>
                            <li><?php _e('Map CSV columns to fields', 'acf-custom-post-importer'); ?></li>
                            <li><?php _e('Validate and start import', 'acf-custom-post-importer'); ?></li>
                        </ol>
                    </div>
                    
                    <div class="sidebar-box">
                        <h3><?php _e('Supported Fields', 'acf-custom-post-importer'); ?></h3>
                        <ul>
                            <li><?php _e('Standard WordPress fields', 'acf-custom-post-importer'); ?></li>
                            <li><?php _e('All ACF field types', 'acf-custom-post-importer'); ?></li>
                            <li><?php _e('Custom taxonomies', 'acf-custom-post-importer'); ?></li>
                            <li><?php _e('Featured images', 'acf-custom-post-importer'); ?></li>
                            <li><?php _e('Post relationships', 'acf-custom-post-importer'); ?></li>
                        </ul>
                    </div>
                    
                    <div class="sidebar-box">
                        <h3><?php _e('Tips', 'acf-custom-post-importer'); ?></h3>
                        <ul>
                            <li><?php _e('Always backup your database first', 'acf-custom-post-importer'); ?></li>
                            <li><?php _e('Test with a small sample first', 'acf-custom-post-importer'); ?></li>
                            <li><?php _e('Use UTF-8 encoding for special characters', 'acf-custom-post-importer'); ?></li>
                            <li><?php _e('Large imports may take time to complete', 'acf-custom-post-importer'); ?></li>
                        </ul>
                    </div>
                </div>
                
            </div>
        </div>
        <?php
    }
}
