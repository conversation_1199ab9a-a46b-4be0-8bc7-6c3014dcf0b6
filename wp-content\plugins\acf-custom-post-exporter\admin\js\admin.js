jQuery(document).ready(function($) {
    'use strict';
    
    // Initialize the plugin
    var ACFCPTExporter = {
        
        init: function() {
            this.loadPostTypes();
            this.bindEvents();
        },
        
        bindEvents: function() {
            $('#post-type-select').on('change', this.onPostTypeChange);
            $('#load-fields-btn').on('click', this.loadFields);
            $('#select-all-fields').on('click', this.selectAllFields);
            $('#deselect-all-fields').on('click', this.deselectAllFields);
            $('#start-export-btn').on('click', this.startExport);
            $('#reset-form-btn').on('click', this.resetForm);
            $('#new-export-btn').on('click', this.resetForm);
        },
        
        loadPostTypes: function() {
            $.ajax({
                url: acf_cpt_exporter.ajax_url,
                type: 'POST',
                data: {
                    action: 'acf_cpt_get_post_types',
                    nonce: acf_cpt_exporter.nonce
                },
                beforeSend: function() {
                    $('#post-type-select').prop('disabled', true);
                },
                success: function(response) {
                    if (response.success) {
                        var $select = $('#post-type-select');
                        $select.empty().append('<option value="">' + acf_cpt_exporter.strings.select_post_type + '</option>');
                        
                        $.each(response.data, function(key, postType) {
                            $select.append('<option value="' + postType.name + '" data-count="' + postType.count + '">' + 
                                         postType.label + ' (' + postType.count + ')</option>');
                        });
                        
                        $select.prop('disabled', false);
                    } else {
                        alert(response.data.message || acf_cpt_exporter.strings.export_error);
                    }
                },
                error: function() {
                    alert(acf_cpt_exporter.strings.export_error);
                    $('#post-type-select').prop('disabled', false);
                }
            });
        },
        
        onPostTypeChange: function() {
            var selectedValue = $(this).val();
            var postCount = $(this).find(':selected').data('count') || 0;
            
            if (selectedValue) {
                $('#post-count').text(postCount);
                $('#post-type-info').show();
                $('#load-fields-btn').prop('disabled', false);
            } else {
                $('#post-type-info').hide();
                $('#load-fields-btn').prop('disabled', true);
                $('#step-2, #step-3').hide();
            }
        },
        
        loadFields: function() {
            var postType = $('#post-type-select').val();
            
            if (!postType) {
                alert(acf_cpt_exporter.strings.select_post_type);
                return;
            }
            
            $.ajax({
                url: acf_cpt_exporter.ajax_url,
                type: 'POST',
                data: {
                    action: 'acf_cpt_get_acf_fields',
                    post_type: postType,
                    nonce: acf_cpt_exporter.nonce
                },
                beforeSend: function() {
                    $('#load-fields-btn').prop('disabled', true).text(acf_cpt_exporter.strings.loading);
                },
                success: function(response) {
                    if (response.success) {
                        ACFCPTExporter.renderFields(response.data, postType);
                        $('#step-2, #step-3').show();
                    } else {
                        alert(response.data.message || acf_cpt_exporter.strings.export_error);
                    }
                },
                error: function() {
                    alert(acf_cpt_exporter.strings.export_error);
                },
                complete: function() {
                    $('#load-fields-btn').prop('disabled', false).text('Load Fields');
                }
            });
        },
        
        renderFields: function(fieldsData, postType) {
            var $standardContainer = $('#standard-fields');
            var $acfContainer = $('#acf-fields');

            // Clear existing fields
            $standardContainer.empty();
            $acfContainer.empty();

            // Render standard fields
            if (fieldsData.standard) {
                $.each(fieldsData.standard, function(key, field) {
                    var fieldHtml = '<label class="field-checkbox">' +
                                  '<input type="checkbox" name="selected_fields[]" value="' + field.name + '"> ' +
                                  field.label + ' <span class="field-type">(' + field.type + ')</span>' +
                                  '</label>';
                    $standardContainer.append(fieldHtml);
                });
            }

            // Render ACF fields
            if (fieldsData.acf && fieldsData.acf.length > 0) {
                var currentGroup = '';
                $.each(fieldsData.acf, function(index, field) {
                    if (field.group !== currentGroup) {
                        if (currentGroup !== '') {
                            $acfContainer.append('</div>');
                        }
                        $acfContainer.append('<h4>' + field.group + '</h4><div class="field-group-fields">');
                        currentGroup = field.group;
                    }

                    var fieldHtml = '<label class="field-checkbox">' +
                                  '<input type="checkbox" name="selected_fields[]" value="' + field.name + '"> ' +
                                  field.label + ' <span class="field-type">(' + field.type + ')</span>' +
                                  '</label>';
                    $acfContainer.append(fieldHtml);
                });

                if (currentGroup !== '') {
                    $acfContainer.append('</div>');
                }
            } else {
                $acfContainer.append('<p><em>No ACF fields found for this post type.</em></p>');
            }
        },
        
        getStandardFields: function(postType) {
            var standardFields = {
                'ID': { name: 'ID', label: 'Post ID', type: 'number' },
                'post_title': { name: 'post_title', label: 'Title', type: 'text' },
                'post_content': { name: 'post_content', label: 'Content', type: 'textarea' },
                'post_excerpt': { name: 'post_excerpt', label: 'Excerpt', type: 'textarea' },
                'post_status': { name: 'post_status', label: 'Status', type: 'select' },
                'post_date': { name: 'post_date', label: 'Date Published', type: 'datetime' },
                'post_modified': { name: 'post_modified', label: 'Date Modified', type: 'datetime' },
                'featured_image': { name: 'featured_image', label: 'Featured Image', type: 'image' },
                'permalink': { name: 'permalink', label: 'Permalink', type: 'url' }
            };
            
            return standardFields;
        },
        
        selectAllFields: function() {
            $('input[name="selected_fields[]"]').prop('checked', true);
        },
        
        deselectAllFields: function() {
            $('input[name="selected_fields[]"]').prop('checked', false);
        },
        
        startExport: function() {
            var selectedFields = [];
            $('input[name="selected_fields[]"]:checked').each(function() {
                selectedFields.push($(this).val());
            });
            
            if (selectedFields.length === 0) {
                alert(acf_cpt_exporter.strings.select_fields);
                return;
            }
            
            if (!confirm(acf_cpt_exporter.strings.confirm_export)) {
                return;
            }
            
            var exportData = {
                action: 'acf_cpt_export_data',
                post_type: $('#post-type-select').val(),
                selected_fields: selectedFields,
                limit: $('#export-limit').val() || '',
                offset: $('#export-offset').val() || 0,
                nonce: acf_cpt_exporter.nonce
            };
            
            // Show progress
            $('#step-3').hide();
            $('#export-progress').show();
            
            $.ajax({
                url: acf_cpt_exporter.ajax_url,
                type: 'POST',
                data: exportData,
                success: function(response) {
                    $('#export-progress').hide();
                    $('#export-results').show();
                    
                    if (response.success) {
                        $('#export-summary').text('Exported ' + response.data.total_records + ' records');
                        $('#download-link').attr('href', response.data.download_url);
                        $('#export-success').show();
                        $('#export-error').hide();
                    } else {
                        $('#error-message').text(response.data.message || acf_cpt_exporter.strings.export_error);
                        $('#export-error').show();
                        $('#export-success').hide();
                    }
                },
                error: function() {
                    $('#export-progress').hide();
                    $('#export-results').show();
                    $('#error-message').text(acf_cpt_exporter.strings.export_error);
                    $('#export-error').show();
                    $('#export-success').hide();
                }
            });
        },
        
        resetForm: function() {
            // Reset form to initial state
            $('#post-type-select').val('');
            $('#export-limit, #export-offset').val('');
            $('#export-offset').val('0');
            
            // Hide all steps except first
            $('#step-2, #step-3, #export-progress, #export-results').hide();
            $('#step-1').show();
            
            // Reset buttons
            $('#load-fields-btn').prop('disabled', true);
            $('#post-type-info').hide();
            
            // Clear fields
            $('#standard-fields, #acf-fields').empty();
        }
    };
    
    // Initialize the plugin
    ACFCPTExporter.init();
});
