<?php
/**
 * Data Exporter Class
 * 
 * Handles the actual data export process
 */

if (!defined('ABSPATH')) {
    exit;
}

class ACF_CPT_Data_Exporter {
    
    /**
     * Export data based on provided parameters
     * 
     * @param array $params
     * @return array
     */
    public function export($params) {
        $post_type = sanitize_text_field($params['post_type']);
        $selected_fields = isset($params['selected_fields']) ? $params['selected_fields'] : array();
        $export_format = isset($params['export_format']) ? sanitize_text_field($params['export_format']) : 'csv';
        $limit = isset($params['limit']) ? intval($params['limit']) : -1;
        $offset = isset($params['offset']) ? intval($params['offset']) : 0;
        
        // Validate post type
        if (!post_type_exists($post_type)) {
            return array(
                'success' => false,
                'message' => __('Invalid post type', 'acf-custom-post-exporter')
            );
        }
        
        // Get posts
        $posts = $this->get_posts($post_type, $limit, $offset);
        
        if (empty($posts)) {
            return array(
                'success' => false,
                'message' => __('No posts found for export', 'acf-custom-post-exporter')
            );
        }
        
        // Prepare data
        $export_data = $this->prepare_export_data($posts, $selected_fields);
        
        // Generate file
        $csv_generator = new ACF_CPT_CSV_Generator();
        $file_result = $csv_generator->generate_csv($export_data, $post_type);
        
        if ($file_result['success']) {
            return array(
                'success' => true,
                'message' => sprintf(__('Successfully exported %d records', 'acf-custom-post-exporter'), count($posts)),
                'filename' => $file_result['filename'],
                'download_url' => $file_result['download_url'],
                'total_records' => count($posts)
            );
        } else {
            return array(
                'success' => false,
                'message' => $file_result['message']
            );
        }
    }
    
    /**
     * Get posts for export
     * 
     * @param string $post_type
     * @param int $limit
     * @param int $offset
     * @return array
     */
    private function get_posts($post_type, $limit = -1, $offset = 0) {
        $args = array(
            'post_type' => $post_type,
            'post_status' => array('publish', 'private', 'draft'),
            'posts_per_page' => $limit,
            'offset' => $offset,
            'orderby' => 'ID',
            'order' => 'ASC',
            'meta_query' => array(),
            'no_found_rows' => true,
            'update_post_meta_cache' => false,
            'update_post_term_cache' => false
        );
        
        return get_posts($args);
    }
    
    /**
     * Prepare export data
     * 
     * @param array $posts
     * @param array $selected_fields
     * @return array
     */
    private function prepare_export_data($posts, $selected_fields) {
        $export_data = array();
        $headers = array();
        
        // Prepare headers
        foreach ($selected_fields as $field) {
            $field_name = sanitize_text_field($field);
            $headers[] = $field_name;
        }
        
        $export_data[] = $headers;
        
        // Process each post
        foreach ($posts as $post) {
            $row_data = array();
            
            foreach ($selected_fields as $field) {
                $field_name = sanitize_text_field($field);
                $value = $this->get_field_value($post, $field_name);
                $row_data[] = $this->format_value_for_export($value, $field_name);
            }
            
            $export_data[] = $row_data;
        }
        
        return $export_data;
    }
    
    /**
     * Get field value for a post
     * 
     * @param WP_Post $post
     * @param string $field_name
     * @return mixed
     */
    private function get_field_value($post, $field_name) {
        // Handle standard WordPress fields
        if (property_exists($post, $field_name)) {
            return $post->$field_name;
        }
        
        // Handle special cases
        switch ($field_name) {
            case 'featured_image':
                return $this->get_featured_image_data($post->ID);
                
            case 'permalink':
                return get_permalink($post->ID);
                
            case 'post_author_name':
                return get_the_author_meta('display_name', $post->post_author);
                
            default:
                // Handle taxonomy fields
                if (strpos($field_name, 'tax_') === 0) {
                    $taxonomy = str_replace('tax_', '', $field_name);
                    return $this->get_taxonomy_terms($post->ID, $taxonomy);
                }
                
                // Handle ACF fields
                if (function_exists('get_field')) {
                    return get_field($field_name, $post->ID, false);
                }
                
                // Handle custom meta fields
                return get_post_meta($post->ID, $field_name, true);
        }
    }
    
    /**
     * Get featured image data
     * 
     * @param int $post_id
     * @return string
     */
    private function get_featured_image_data($post_id) {
        $thumbnail_id = get_post_thumbnail_id($post_id);
        if ($thumbnail_id) {
            $image_url = wp_get_attachment_url($thumbnail_id);
            $image_alt = get_post_meta($thumbnail_id, '_wp_attachment_image_alt', true);
            return $image_url . '|' . $image_alt;
        }
        return '';
    }
    
    /**
     * Get taxonomy terms for a post
     * 
     * @param int $post_id
     * @param string $taxonomy
     * @return string
     */
    private function get_taxonomy_terms($post_id, $taxonomy) {
        $terms = get_the_terms($post_id, $taxonomy);
        if ($terms && !is_wp_error($terms)) {
            $term_names = array();
            foreach ($terms as $term) {
                $term_names[] = $term->name;
            }
            return implode(', ', $term_names);
        }
        return '';
    }
    
    /**
     * Format value for export
     * 
     * @param mixed $value
     * @param string $field_name
     * @return string
     */
    private function format_value_for_export($value, $field_name) {
        if (is_null($value)) {
            return '';
        }
        
        if (is_array($value)) {
            return $this->format_array_value($value);
        }
        
        if (is_object($value)) {
            return $this->format_object_value($value);
        }
        
        if (is_bool($value)) {
            return $value ? 'Yes' : 'No';
        }
        
        // Clean up the value
        $value = strip_tags($value);
        $value = str_replace(array("\r", "\n"), ' ', $value);
        $value = trim($value);
        
        return $value;
    }
    
    /**
     * Format array values
     * 
     * @param array $value
     * @return string
     */
    private function format_array_value($value) {
        $formatted_items = array();
        
        foreach ($value as $item) {
            if (is_array($item) || is_object($item)) {
                $formatted_items[] = json_encode($item);
            } else {
                $formatted_items[] = $item;
            }
        }
        
        return implode(', ', $formatted_items);
    }
    
    /**
     * Format object values
     * 
     * @param object $value
     * @return string
     */
    private function format_object_value($value) {
        if (isset($value->post_title)) {
            return $value->post_title;
        }
        
        if (isset($value->name)) {
            return $value->name;
        }
        
        if (isset($value->title)) {
            return $value->title;
        }
        
        return json_encode($value);
    }
}
