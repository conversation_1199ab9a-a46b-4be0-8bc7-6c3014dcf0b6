/* ACF Custom Post Type Importer Admin Styles */

.acf-cpt-importer-container {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.acf-cpt-importer-main {
    flex: 1;
    max-width: 900px;
}

.acf-cpt-importer-sidebar {
    width: 300px;
    flex-shrink: 0;
}

/* Steps */
.acf-cpt-step {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.acf-cpt-step h2 {
    margin-top: 0;
    color: #23282d;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
}

/* Upload Area */
.upload-area {
    margin-bottom: 20px;
}

.upload-box {
    border: 2px dashed #ccd0d4;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.upload-box:hover {
    border-color: #0073aa;
    background: #f0f6fc;
}

.upload-box.dragover {
    border-color: #0073aa;
    background: #e6f3ff;
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.6;
}

.upload-box input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-info {
    background: #f0f6fc;
    border: 1px solid #c3dbf0;
    border-radius: 4px;
    padding: 15px;
    margin: 15px 0;
}

.upload-info ul {
    margin: 10px 0 0 20px;
}

.upload-info li {
    margin-bottom: 5px;
    color: #666;
}

/* Post Type Selection */
.post-type-selection {
    margin-bottom: 20px;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
}

.post-type-selection label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

/* CSV Preview */
.csv-preview {
    margin-bottom: 20px;
}

.preview-info {
    background: #f0f6fc;
    border: 1px solid #c3dbf0;
    border-radius: 4px;
    padding: 10px 15px;
    margin-bottom: 15px;
    display: flex;
    gap: 30px;
}

.preview-table-container {
    max-height: 300px;
    overflow: auto;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
}

#preview-table {
    margin: 0;
}

#preview-table th,
#preview-table td {
    padding: 8px 12px;
    border-right: 1px solid #e1e1e1;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Field Mapping */
.field-mapping {
    margin-bottom: 20px;
}

.mapping-controls {
    margin-bottom: 15px;
    display: flex;
    gap: 10px;
}

#mapping-table-container {
    max-height: 400px;
    overflow: auto;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
}

#mapping-table {
    margin: 0;
}

#mapping-table th,
#mapping-table td {
    padding: 10px 12px;
    border-right: 1px solid #e1e1e1;
}

#mapping-table th:nth-child(1),
#mapping-table td:nth-child(1) {
    width: 20%;
}

#mapping-table th:nth-child(2),
#mapping-table td:nth-child(2) {
    width: 25%;
}

#mapping-table th:nth-child(3),
#mapping-table td:nth-child(3) {
    width: 30%;
}

#mapping-table th:nth-child(4),
#mapping-table td:nth-child(4) {
    width: 25%;
}

.field-mapping-select {
    width: 100%;
    max-width: 250px;
}

.field-type-info {
    font-size: 12px;
    color: #666;
}

.field-type-info small {
    display: block;
    margin-top: 3px;
    font-style: italic;
}

/* Import Options */
.import-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

/* Validation Results */
.validation-results {
    margin-bottom: 20px;
}

.validation-results .notice {
    margin: 10px 0;
    padding: 12px;
}

.validation-results ul {
    margin: 10px 0 0 20px;
}

.import-summary {
    background: #f0f6fc;
    border: 1px solid #c3dbf0;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
}

.import-summary ul {
    margin: 10px 0 0 20px;
}

/* Progress */
.progress-container {
    text-align: center;
    padding: 20px;
}

.progress-bar {
    width: 100%;
    height: 25px;
    background-color: #f0f0f0;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 20px;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005a87);
    transition: width 0.3s ease;
    border-radius: 12px;
}

.progress-stats {
    margin-top: 15px;
}

.progress-text {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #23282d;
}

.progress-numbers {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 15px;
}

.progress-numbers span {
    background: #f9f9f9;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
}

/* Results */
#import-results .notice {
    padding: 15px;
    margin: 15px 0;
}

#import-results h3 {
    margin-top: 0;
}

/* Sidebar */
.sidebar-box {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.sidebar-box h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #23282d;
    font-size: 14px;
}

.sidebar-box ol,
.sidebar-box ul {
    margin: 0;
    padding-left: 20px;
}

.sidebar-box li {
    margin-bottom: 8px;
    color: #666;
    font-size: 13px;
    line-height: 1.4;
}

/* Responsive */
@media (max-width: 1200px) {
    .acf-cpt-importer-container {
        flex-direction: column;
    }
    
    .acf-cpt-importer-sidebar {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .preview-info {
        flex-direction: column;
        gap: 10px;
    }
    
    .mapping-controls {
        flex-direction: column;
    }
    
    .import-actions {
        flex-direction: column;
    }
    
    .progress-numbers {
        flex-direction: column;
        align-items: center;
    }
    
    #mapping-table th,
    #mapping-table td {
        padding: 8px 6px;
        font-size: 12px;
    }
    
    .field-mapping-select {
        max-width: 100%;
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Button enhancements */
.button-large {
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
}

/* Form table adjustments */
.form-table th {
    width: 200px;
}

.form-table td .description {
    margin-top: 5px;
    color: #666;
    font-style: italic;
}

/* Drag and drop enhancements */
.upload-box.dragover .upload-icon {
    animation: bounce 0.5s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    80% {
        transform: translateY(-5px);
    }
}

/* Table improvements */
.wp-list-table th {
    background: #f9f9f9;
    font-weight: 600;
}

.wp-list-table tbody tr:nth-child(even) {
    background: #f9f9f9;
}

/* Notice improvements */
.notice h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
}

.notice ul {
    margin-bottom: 0;
}

/* Progress bar animation */
.progress-fill {
    background: linear-gradient(90deg, #0073aa, #005a87, #0073aa);
    background-size: 200% 100%;
    animation: progressShine 2s linear infinite;
}

@keyframes progressShine {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}
