# Troubleshooting Guide - WooCommerce ACF Product Exporter

## Download Issues

### Problem: "No file is downloading"

This is the most common issue. Here's how to diagnose and fix it:

#### Step 1: Check Debug Information
1. Go to WooCommerce > ACF Product Exporter
2. Look at the debug info box at the top of the page
3. Verify all items show "Yes":
   - Export Dir Writable: Yes
   - WooCommerce Active: Yes
   - ACF Active: Yes (if you want ACF features)

#### Step 2: Test Basic Functionality
1. Click the "Test Export" button
2. Check browser console (F12) for any JavaScript errors
3. If test export works, the issue is with the main export process
4. If test export fails, there's a fundamental issue

#### Step 3: Check File Permissions
```bash
# Check uploads directory permissions
ls -la wp-content/uploads/

# Should show something like:
drwxr-xr-x wordpress wordpress wp-content/uploads/

# If not writable, fix permissions:
chmod 755 wp-content/uploads/
chown -R www-data:www-data wp-content/uploads/
```

#### Step 4: Check PHP Error Logs
1. Enable WordPress debug mode in wp-config.php:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

2. Check `/wp-content/debug.log` for errors

#### Step 5: Browser Console Check
1. Open browser developer tools (F12)
2. Go to Console tab
3. Try the export again
4. Look for JavaScript errors or AJAX failures

### Common Fixes

#### Fix 1: Memory Issues
Add to wp-config.php:
```php
ini_set('memory_limit', '512M');
ini_set('max_execution_time', 600);
```

#### Fix 2: File Path Issues
The plugin creates files in `/wp-content/uploads/wc-acf-exports/`
- Ensure this directory can be created
- Check if your hosting has restrictions on file creation

#### Fix 3: AJAX Issues
- Check if admin-ajax.php is accessible
- Verify nonce verification is working
- Test with a different browser

#### Fix 4: Server Configuration
Some servers block file downloads. Check:
- .htaccess rules
- Server security settings
- Hosting provider restrictions

## Export Process Issues

### Problem: Export starts but never completes

#### Check 1: PHP Timeout
- Increase `max_execution_time` in PHP settings
- Use smaller batch sizes (10-25 products)

#### Check 2: Memory Usage
- Monitor memory usage during export
- Reduce batch size if memory errors occur

#### Check 3: Database Issues
- Check if database queries are timing out
- Verify database connection is stable

### Problem: Missing ACF Data

#### Check 1: ACF Pro Installation
- Ensure ACF Pro is installed and active
- Verify ACF fields are properly configured

#### Check 2: Field Group Locations
- Check that ACF field groups are assigned to products
- Verify field names are correct

#### Check 3: Field Permissions
- Ensure current user can access ACF fields
- Check field group permissions

## Manual Testing

### Test 1: Create Simple Export File
```php
// Add this to functions.php temporarily
function test_wc_acf_export() {
    $upload_dir = wp_upload_dir();
    $test_file = $upload_dir['basedir'] . '/test-export.csv';
    
    $data = "ID,Name\n1,Test Product\n";
    file_put_contents($test_file, $data);
    
    if (file_exists($test_file)) {
        echo "File created successfully at: " . $test_file;
    } else {
        echo "Failed to create file";
    }
}
add_action('wp_footer', 'test_wc_acf_export');
```

### Test 2: Check Download URL
1. Copy the download URL from browser console
2. Paste it directly in browser address bar
3. See if file downloads or shows error

### Test 3: Manual File Access
1. Go to `/wp-content/uploads/wc-acf-exports/` via FTP
2. Check if export files are being created
3. Download files manually to verify content

## Advanced Debugging

### Enable Detailed Logging
Add this to the plugin's main file:
```php
// Add after line 1 in woocommerce-acf-product-exporter.php
error_log('WC ACF Exporter: Plugin loaded');
```

### Check AJAX Responses
1. Open Network tab in browser dev tools
2. Start export
3. Look for AJAX requests to admin-ajax.php
4. Check response status and content

### Verify File Creation
Add debugging to CSV exporter:
```php
// In class-csv-exporter.php, after file creation
error_log('Export file created: ' . $file_path);
error_log('File exists: ' . (file_exists($file_path) ? 'yes' : 'no'));
error_log('File size: ' . filesize($file_path));
```

## Getting Help

### Information to Provide
When seeking help, provide:
1. WordPress version
2. WooCommerce version
3. ACF Pro version
4. PHP version
5. Server type (Apache/Nginx)
6. Hosting provider
7. Error messages from debug.log
8. Browser console errors
9. Results from debug info box

### Quick Diagnostic
Run this in WordPress admin:
```php
// Add to functions.php temporarily
function wc_acf_diagnostic() {
    echo '<div style="background: #fff; padding: 20px; margin: 20px;">';
    echo '<h3>WC ACF Exporter Diagnostic</h3>';
    echo '<p>WordPress: ' . get_bloginfo('version') . '</p>';
    echo '<p>WooCommerce: ' . (class_exists('WooCommerce') ? WC()->version : 'Not installed') . '</p>';
    echo '<p>ACF: ' . (function_exists('acf') ? acf()->settings['version'] : 'Not installed') . '</p>';
    echo '<p>PHP: ' . phpversion() . '</p>';
    echo '<p>Memory Limit: ' . ini_get('memory_limit') . '</p>';
    echo '<p>Max Execution Time: ' . ini_get('max_execution_time') . '</p>';
    
    $upload_dir = wp_upload_dir();
    echo '<p>Uploads Dir: ' . $upload_dir['basedir'] . '</p>';
    echo '<p>Uploads Writable: ' . (is_writable($upload_dir['basedir']) ? 'Yes' : 'No') . '</p>';
    echo '</div>';
}
add_action('admin_notices', 'wc_acf_diagnostic');
```

## Last Resort Solutions

### Solution 1: Alternative Download Method
If downloads still don't work, modify the download URL to point directly to the file:
```php
// In get_download_url() method
$upload_url = wp_upload_dir()['baseurl'];
return $upload_url . '/wc-acf-exports/' . $filename;
```

### Solution 2: Email Export
Modify the plugin to email the CSV file instead of downloading.

### Solution 3: FTP Access
Access exported files directly via FTP from `/wp-content/uploads/wc-acf-exports/`

Remember to remove any debugging code after troubleshooting!
