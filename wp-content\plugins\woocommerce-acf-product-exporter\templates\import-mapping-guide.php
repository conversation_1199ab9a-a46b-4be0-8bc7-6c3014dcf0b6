<?php
/**
 * Import Mapping Guide Template
 * This file provides a reference for mapping exported fields during import
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get field mapping guide for import
 */
function wc_acf_get_import_mapping_guide() {
    return array(
        'core_fields' => array(
            'ID' => array(
                'wc_field' => 'ID',
                'description' => 'Product ID (for updates)',
                'required' => false,
                'type' => 'number'
            ),
            'post_title' => array(
                'wc_field' => 'Name',
                'description' => 'Product name/title',
                'required' => true,
                'type' => 'text'
            ),
            'post_content' => array(
                'wc_field' => 'Description',
                'description' => 'Product description',
                'required' => false,
                'type' => 'textarea'
            ),
            'post_excerpt' => array(
                'wc_field' => 'Short description',
                'description' => 'Product short description',
                'required' => false,
                'type' => 'textarea'
            ),
            'post_status' => array(
                'wc_field' => 'Published',
                'description' => 'Product status (1 for published)',
                'required' => false,
                'type' => 'boolean'
            ),
            'sku' => array(
                'wc_field' => 'SKU',
                'description' => 'Product SKU',
                'required' => false,
                'type' => 'text'
            ),
            'regular_price' => array(
                'wc_field' => 'Regular price',
                'description' => 'Product regular price',
                'required' => false,
                'type' => 'decimal'
            ),
            'sale_price' => array(
                'wc_field' => 'Sale price',
                'description' => 'Product sale price',
                'required' => false,
                'type' => 'decimal'
            ),
            'stock_quantity' => array(
                'wc_field' => 'Stock',
                'description' => 'Stock quantity',
                'required' => false,
                'type' => 'number'
            ),
            'manage_stock' => array(
                'wc_field' => 'Manage stock?',
                'description' => 'Enable stock management',
                'required' => false,
                'type' => 'boolean'
            ),
            'stock_status' => array(
                'wc_field' => 'In stock?',
                'description' => 'Stock status',
                'required' => false,
                'type' => 'boolean'
            ),
            'weight' => array(
                'wc_field' => 'Weight',
                'description' => 'Product weight',
                'required' => false,
                'type' => 'decimal'
            ),
            'length' => array(
                'wc_field' => 'Length',
                'description' => 'Product length',
                'required' => false,
                'type' => 'decimal'
            ),
            'width' => array(
                'wc_field' => 'Width',
                'description' => 'Product width',
                'required' => false,
                'type' => 'decimal'
            ),
            'height' => array(
                'wc_field' => 'Height',
                'description' => 'Product height',
                'required' => false,
                'type' => 'decimal'
            ),
            'featured_image' => array(
                'wc_field' => 'Images',
                'description' => 'Featured image URL',
                'required' => false,
                'type' => 'url'
            ),
            'product_gallery' => array(
                'wc_field' => 'Images',
                'description' => 'Gallery image URLs (comma-separated)',
                'required' => false,
                'type' => 'url_list'
            )
        ),
        
        'taxonomy_fields' => array(
            'taxonomy_product_cat' => array(
                'wc_field' => 'Categories',
                'description' => 'Product categories (comma-separated)',
                'required' => false,
                'type' => 'taxonomy'
            ),
            'taxonomy_product_tag' => array(
                'wc_field' => 'Tags',
                'description' => 'Product tags (comma-separated)',
                'required' => false,
                'type' => 'taxonomy'
            ),
            'taxonomy_product_shipping_class' => array(
                'wc_field' => 'Shipping class',
                'description' => 'Product shipping class',
                'required' => false,
                'type' => 'taxonomy'
            )
        ),
        
        'acf_field_types' => array(
            'text' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Simple text field',
                'format' => 'Plain text'
            ),
            'textarea' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Multi-line text field',
                'format' => 'Plain text with line breaks'
            ),
            'number' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Numeric field',
                'format' => 'Number (integer or decimal)'
            ),
            'email' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Email field',
                'format' => 'Valid email address'
            ),
            'url' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'URL field',
                'format' => 'Valid URL'
            ),
            'password' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Password field',
                'format' => 'Plain text (will be hashed)'
            ),
            'image' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Image field',
                'format' => 'Image URL or attachment ID'
            ),
            'gallery' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Image gallery field',
                'format' => 'Comma-separated image URLs or IDs'
            ),
            'file' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'File field',
                'format' => 'File URL or attachment ID'
            ),
            'select' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Select dropdown field',
                'format' => 'Option value'
            ),
            'checkbox' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Checkbox field',
                'format' => 'Comma-separated values for multiple'
            ),
            'radio' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Radio button field',
                'format' => 'Selected option value'
            ),
            'true_false' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'True/False field',
                'format' => '1 for true, 0 for false'
            ),
            'date_picker' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Date picker field',
                'format' => 'YYYY-MM-DD format'
            ),
            'date_time_picker' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Date and time picker field',
                'format' => 'YYYY-MM-DD HH:MM:SS format'
            ),
            'time_picker' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Time picker field',
                'format' => 'HH:MM:SS format'
            ),
            'color_picker' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Color picker field',
                'format' => 'Hex color code (#ffffff)'
            ),
            'post_object' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Post object field',
                'format' => 'Post ID or comma-separated IDs'
            ),
            'page_link' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Page link field',
                'format' => 'Post ID or URL'
            ),
            'relationship' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Relationship field',
                'format' => 'Comma-separated post IDs'
            ),
            'taxonomy' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Taxonomy field',
                'format' => 'Comma-separated term names or IDs'
            ),
            'user' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'User field',
                'format' => 'User ID or comma-separated IDs'
            ),
            'google_map' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Google Map field',
                'format' => 'JSON object with lat, lng, address'
            ),
            'repeater' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Repeater field',
                'format' => 'Structured format (see export)'
            ),
            'flexible_content' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Flexible content field',
                'format' => 'JSON format (see export)'
            ),
            'group' => array(
                'mapping' => 'Meta: field_name',
                'description' => 'Group field',
                'format' => 'Pipe-separated sub-fields'
            )
        ),
        
        'variation_fields' => array(
            'parent_id' => array(
                'description' => 'Parent product ID for variations',
                'mapping' => 'Parent'
            ),
            'variation_type' => array(
                'description' => 'Type: parent or variation',
                'mapping' => 'Type'
            ),
            'attribute_*' => array(
                'description' => 'Variation attributes',
                'mapping' => 'Attribute: attribute_name'
            ),
            'variation_image' => array(
                'description' => 'Variation-specific image',
                'mapping' => 'Images'
            )
        )
    );
}

/**
 * Generate import mapping CSV template
 */
function wc_acf_generate_mapping_template() {
    $mapping_guide = wc_acf_get_import_mapping_guide();
    
    $template = "Export Field,WooCommerce Field,Description,Required,Type\n";
    
    // Core fields
    foreach ($mapping_guide['core_fields'] as $export_field => $field_info) {
        $template .= sprintf(
            '"%s","%s","%s","%s","%s"' . "\n",
            $export_field,
            $field_info['wc_field'],
            $field_info['description'],
            $field_info['required'] ? 'Yes' : 'No',
            $field_info['type']
        );
    }
    
    // Taxonomy fields
    foreach ($mapping_guide['taxonomy_fields'] as $export_field => $field_info) {
        $template .= sprintf(
            '"%s","%s","%s","%s","%s"' . "\n",
            $export_field,
            $field_info['wc_field'],
            $field_info['description'],
            $field_info['required'] ? 'Yes' : 'No',
            $field_info['type']
        );
    }
    
    // ACF field examples
    $template .= "\n# ACF Fields (remove acf_ prefix when mapping)\n";
    foreach ($mapping_guide['acf_field_types'] as $field_type => $field_info) {
        $template .= sprintf(
            '"acf_%s_example","%s","%s - %s","No","acf_%s"' . "\n",
            $field_type,
            $field_info['mapping'],
            $field_info['description'],
            $field_info['format'],
            $field_type
        );
    }
    
    return $template;
}
?>
