<?php
/**
 * Field Mapper Class
 * 
 * Maps CSV columns to WordPress/ACF fields
 */

if (!defined('ABSPATH')) {
    exit;
}

class ACF_CPT_Field_Mapper {
    
    /**
     * Get available fields for a post type
     * 
     * @param string $post_type
     * @return array
     */
    public function get_available_fields($post_type) {
        $fields = array();
        
        // Standard WordPress fields
        $fields['standard'] = $this->get_standard_fields($post_type);
        
        // ACF fields
        $fields['acf'] = $this->get_acf_fields($post_type);
        
        return $fields;
    }
    
    /**
     * Get standard WordPress fields
     * 
     * @param string $post_type
     * @return array
     */
    private function get_standard_fields($post_type) {
        $standard_fields = array(
            'ID' => array(
                'name' => 'ID',
                'label' => 'Post ID',
                'type' => 'number',
                'required' => false,
                'description' => 'Existing post ID for updates'
            ),
            'post_title' => array(
                'name' => 'post_title',
                'label' => 'Title',
                'type' => 'text',
                'required' => true,
                'description' => 'Post title'
            ),
            'post_content' => array(
                'name' => 'post_content',
                'label' => 'Content',
                'type' => 'textarea',
                'required' => false,
                'description' => 'Post content/description'
            ),
            'post_excerpt' => array(
                'name' => 'post_excerpt',
                'label' => 'Excerpt',
                'type' => 'textarea',
                'required' => false,
                'description' => 'Post excerpt'
            ),
            'post_status' => array(
                'name' => 'post_status',
                'label' => 'Status',
                'type' => 'select',
                'required' => false,
                'description' => 'Post status (publish, draft, private)',
                'options' => array('publish', 'draft', 'private', 'pending')
            ),
            'post_date' => array(
                'name' => 'post_date',
                'label' => 'Date Published',
                'type' => 'datetime',
                'required' => false,
                'description' => 'Publication date (YYYY-MM-DD HH:MM:SS)'
            ),
            'post_author' => array(
                'name' => 'post_author',
                'label' => 'Author',
                'type' => 'number',
                'required' => false,
                'description' => 'Author user ID'
            ),
            'menu_order' => array(
                'name' => 'menu_order',
                'label' => 'Menu Order',
                'type' => 'number',
                'required' => false,
                'description' => 'Order for sorting'
            )
        );
        
        // Add featured image for post types that support it
        if (post_type_supports($post_type, 'thumbnail')) {
            $standard_fields['featured_image'] = array(
                'name' => 'featured_image',
                'label' => 'Featured Image',
                'type' => 'image',
                'required' => false,
                'description' => 'Featured image URL or attachment ID'
            );
        }
        
        // Add taxonomies for the post type
        $taxonomies = get_object_taxonomies($post_type, 'objects');
        foreach ($taxonomies as $taxonomy) {
            $standard_fields['tax_' . $taxonomy->name] = array(
                'name' => 'tax_' . $taxonomy->name,
                'label' => $taxonomy->labels->name,
                'type' => 'taxonomy',
                'required' => false,
                'description' => 'Comma-separated list of ' . strtolower($taxonomy->labels->name)
            );
        }
        
        return $standard_fields;
    }
    
    /**
     * Get ACF fields for a post type
     * 
     * @param string $post_type
     * @return array
     */
    private function get_acf_fields($post_type) {
        if (!function_exists('acf_get_field_groups')) {
            return array();
        }
        
        $field_groups = acf_get_field_groups(array(
            'post_type' => $post_type
        ));
        
        $acf_fields = array();
        
        foreach ($field_groups as $field_group) {
            $fields = acf_get_fields($field_group);
            
            if ($fields) {
                foreach ($fields as $field) {
                    $acf_fields[$field['name']] = array(
                        'name' => $field['name'],
                        'label' => $field['label'],
                        'type' => $field['type'],
                        'required' => isset($field['required']) ? $field['required'] : false,
                        'description' => $this->get_field_description($field),
                        'group' => $field_group['title']
                    );
                }
            }
        }
        
        return $acf_fields;
    }
    
    /**
     * Get field description based on field type
     * 
     * @param array $field
     * @return string
     */
    private function get_field_description($field) {
        switch ($field['type']) {
            case 'select':
            case 'checkbox':
            case 'radio':
                if (isset($field['choices']) && is_array($field['choices'])) {
                    return 'Options: ' . implode(', ', array_keys($field['choices']));
                }
                break;
                
            case 'true_false':
                return 'Values: 1 (Yes), 0 (No)';
                
            case 'date_picker':
                return 'Format: YYYY-MM-DD';
                
            case 'date_time_picker':
                return 'Format: YYYY-MM-DD HH:MM:SS';
                
            case 'time_picker':
                return 'Format: HH:MM:SS';
                
            case 'image':
            case 'file':
                return 'URL or attachment ID';
                
            case 'gallery':
                return 'Comma-separated URLs or attachment IDs';
                
            case 'post_object':
            case 'relationship':
                return 'Post ID or comma-separated post IDs';
                
            case 'user':
                return 'User ID or username';
                
            case 'taxonomy':
                return 'Term ID, slug, or name';
                
            case 'repeater':
                return 'JSON format for repeater data';
                
            case 'group':
                return 'JSON format for group data';
        }
        
        return 'Enter value for ' . $field['label'];
    }
    
    /**
     * Auto-map CSV headers to fields
     * 
     * @param array $csv_headers
     * @param array $available_fields
     * @return array
     */
    public function auto_map_fields($csv_headers, $available_fields) {
        $mapping = array();
        $all_fields = array_merge($available_fields['standard'], $available_fields['acf']);
        
        foreach ($csv_headers as $header) {
            $header_clean = strtolower(trim($header));
            
            // Direct match
            if (isset($all_fields[$header])) {
                $mapping[$header] = $header;
                continue;
            }
            
            // Fuzzy matching
            $best_match = $this->find_best_field_match($header_clean, $all_fields);
            if ($best_match) {
                $mapping[$header] = $best_match;
            }
        }
        
        return $mapping;
    }
    
    /**
     * Find best field match for a CSV header
     * 
     * @param string $header
     * @param array $fields
     * @return string|null
     */
    private function find_best_field_match($header, $fields) {
        $header = strtolower(str_replace(array(' ', '_', '-'), '', $header));
        
        // Common field mappings
        $common_mappings = array(
            'title' => 'post_title',
            'name' => 'post_title',
            'content' => 'post_content',
            'description' => 'post_content',
            'excerpt' => 'post_excerpt',
            'summary' => 'post_excerpt',
            'status' => 'post_status',
            'date' => 'post_date',
            'published' => 'post_date',
            'author' => 'post_author',
            'image' => 'featured_image',
            'thumbnail' => 'featured_image',
            'featuredimage' => 'featured_image'
        );
        
        if (isset($common_mappings[$header])) {
            return $common_mappings[$header];
        }
        
        // Look for partial matches in field names and labels
        foreach ($fields as $field_name => $field_data) {
            $field_name_clean = strtolower(str_replace(array(' ', '_', '-'), '', $field_name));
            $field_label_clean = strtolower(str_replace(array(' ', '_', '-'), '', $field_data['label']));
            
            if (strpos($field_name_clean, $header) !== false || 
                strpos($header, $field_name_clean) !== false ||
                strpos($field_label_clean, $header) !== false || 
                strpos($header, $field_label_clean) !== false) {
                return $field_name;
            }
        }
        
        return null;
    }
    
    /**
     * Validate field mapping
     * 
     * @param array $mapping
     * @param array $available_fields
     * @return array
     */
    public function validate_mapping($mapping, $available_fields) {
        $errors = array();
        $warnings = array();
        $all_fields = array_merge($available_fields['standard'], $available_fields['acf']);
        
        // Check if required fields are mapped
        foreach ($all_fields as $field_name => $field_data) {
            if ($field_data['required'] && !in_array($field_name, $mapping)) {
                $errors[] = sprintf(__('Required field "%s" is not mapped', 'acf-custom-post-importer'), $field_data['label']);
            }
        }
        
        // Check if mapped fields exist
        foreach ($mapping as $csv_header => $field_name) {
            if ($field_name && !isset($all_fields[$field_name])) {
                $warnings[] = sprintf(__('Field "%s" does not exist for this post type', 'acf-custom-post-importer'), $field_name);
            }
        }
        
        return array(
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        );
    }
}
