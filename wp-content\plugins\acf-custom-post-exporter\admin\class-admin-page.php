<?php
/**
 * Admin Page Class
 * 
 * Handles the admin interface for the plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

class ACF_CPT_Exporter_Admin_Page {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_management_page(
            __('ACF Custom Post Exporter', 'acf-custom-post-exporter'),
            __('ACF Post Exporter', 'acf-custom-post-exporter'),
            'manage_options',
            'acf-cpt-exporter',
            array($this, 'admin_page_content')
        );
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        if ($hook !== 'tools_page_acf-cpt-exporter') {
            return;
        }
        
        wp_enqueue_script(
            'acf-cpt-exporter-admin',
            ACF_CPT_EXPORTER_PLUGIN_URL . 'admin/js/admin.js',
            array('jquery'),
            ACF_CPT_EXPORTER_VERSION,
            true
        );
        
        wp_enqueue_style(
            'acf-cpt-exporter-admin',
            ACF_CPT_EXPORTER_PLUGIN_URL . 'admin/css/admin.css',
            array(),
            ACF_CPT_EXPORTER_VERSION
        );
        
        wp_localize_script('acf-cpt-exporter-admin', 'acf_cpt_exporter', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('acf_cpt_export_nonce'),
            'strings' => array(
                'loading' => __('Loading...', 'acf-custom-post-exporter'),
                'select_post_type' => __('Please select a post type', 'acf-custom-post-exporter'),
                'select_fields' => __('Please select at least one field', 'acf-custom-post-exporter'),
                'export_success' => __('Export completed successfully!', 'acf-custom-post-exporter'),
                'export_error' => __('Export failed. Please try again.', 'acf-custom-post-exporter'),
                'confirm_export' => __('Are you sure you want to export the selected data?', 'acf-custom-post-exporter')
            )
        ));
    }
    
    /**
     * Admin page content
     */
    public function admin_page_content() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <div class="acf-cpt-exporter-container">
                <div class="acf-cpt-exporter-main">
                    
                    <!-- Step 1: Select Post Type -->
                    <div class="acf-cpt-step" id="step-1">
                        <h2><?php _e('Step 1: Select Post Type', 'acf-custom-post-exporter'); ?></h2>
                        <p><?php _e('Choose the post type you want to export data from:', 'acf-custom-post-exporter'); ?></p>
                        
                        <div class="post-type-selection">
                            <select id="post-type-select" class="regular-text">
                                <option value=""><?php _e('Select a post type...', 'acf-custom-post-exporter'); ?></option>
                            </select>
                            <button type="button" id="load-fields-btn" class="button button-primary" disabled>
                                <?php _e('Load Fields', 'acf-custom-post-exporter'); ?>
                            </button>
                        </div>
                        
                        <div id="post-type-info" class="post-type-info" style="display: none;">
                            <p><strong><?php _e('Post Count:', 'acf-custom-post-exporter'); ?></strong> <span id="post-count">0</span></p>
                        </div>
                    </div>
                    
                    <!-- Step 2: Select Fields -->
                    <div class="acf-cpt-step" id="step-2" style="display: none;">
                        <h2><?php _e('Step 2: Select Fields to Export', 'acf-custom-post-exporter'); ?></h2>
                        <p><?php _e('Choose which fields you want to include in the export:', 'acf-custom-post-exporter'); ?></p>
                        
                        <div class="fields-selection">
                            <div class="field-groups">
                                <div class="field-group">
                                    <h3><?php _e('Standard Fields', 'acf-custom-post-exporter'); ?></h3>
                                    <div id="standard-fields" class="fields-list">
                                        <!-- Standard fields will be loaded here -->
                                    </div>
                                </div>
                                
                                <div class="field-group">
                                    <h3><?php _e('ACF Fields', 'acf-custom-post-exporter'); ?></h3>
                                    <div id="acf-fields" class="fields-list">
                                        <!-- ACF fields will be loaded here -->
                                    </div>
                                </div>
                            </div>
                            
                            <div class="field-actions">
                                <button type="button" id="select-all-fields" class="button">
                                    <?php _e('Select All', 'acf-custom-post-exporter'); ?>
                                </button>
                                <button type="button" id="deselect-all-fields" class="button">
                                    <?php _e('Deselect All', 'acf-custom-post-exporter'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step 3: Export Options -->
                    <div class="acf-cpt-step" id="step-3" style="display: none;">
                        <h2><?php _e('Step 3: Export Options', 'acf-custom-post-exporter'); ?></h2>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="export-limit"><?php _e('Limit Records', 'acf-custom-post-exporter'); ?></label>
                                </th>
                                <td>
                                    <input type="number" id="export-limit" class="regular-text" min="1" placeholder="<?php _e('Leave empty for all records', 'acf-custom-post-exporter'); ?>">
                                    <p class="description"><?php _e('Limit the number of records to export (optional)', 'acf-custom-post-exporter'); ?></p>
                                </td>
                            </tr>
                            <tr>
                                <th scope="row">
                                    <label for="export-offset"><?php _e('Offset', 'acf-custom-post-exporter'); ?></label>
                                </th>
                                <td>
                                    <input type="number" id="export-offset" class="regular-text" min="0" value="0">
                                    <p class="description"><?php _e('Number of records to skip from the beginning', 'acf-custom-post-exporter'); ?></p>
                                </td>
                            </tr>
                        </table>
                        
                        <div class="export-actions">
                            <button type="button" id="start-export-btn" class="button button-primary button-large">
                                <?php _e('Start Export', 'acf-custom-post-exporter'); ?>
                            </button>
                            <button type="button" id="reset-form-btn" class="button">
                                <?php _e('Reset', 'acf-custom-post-exporter'); ?>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Export Progress -->
                    <div class="acf-cpt-step" id="export-progress" style="display: none;">
                        <h2><?php _e('Export in Progress', 'acf-custom-post-exporter'); ?></h2>
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 0%;"></div>
                            </div>
                            <p class="progress-text"><?php _e('Preparing export...', 'acf-custom-post-exporter'); ?></p>
                        </div>
                    </div>
                    
                    <!-- Export Results -->
                    <div class="acf-cpt-step" id="export-results" style="display: none;">
                        <h2><?php _e('Export Complete', 'acf-custom-post-exporter'); ?></h2>
                        <div id="export-success" class="notice notice-success" style="display: none;">
                            <p><strong><?php _e('Export completed successfully!', 'acf-custom-post-exporter'); ?></strong></p>
                            <p id="export-summary"></p>
                            <p>
                                <a href="#" id="download-link" class="button button-primary" target="_blank">
                                    <?php _e('Download CSV File', 'acf-custom-post-exporter'); ?>
                                </a>
                            </p>
                        </div>
                        
                        <div id="export-error" class="notice notice-error" style="display: none;">
                            <p><strong><?php _e('Export failed:', 'acf-custom-post-exporter'); ?></strong></p>
                            <p id="error-message"></p>
                        </div>
                        
                        <button type="button" id="new-export-btn" class="button">
                            <?php _e('Start New Export', 'acf-custom-post-exporter'); ?>
                        </button>
                    </div>
                    
                </div>
                
                <!-- Sidebar -->
                <div class="acf-cpt-exporter-sidebar">
                    <div class="sidebar-box">
                        <h3><?php _e('About This Plugin', 'acf-custom-post-exporter'); ?></h3>
                        <p><?php _e('This plugin allows you to export ACF field values from any custom post type to CSV format.', 'acf-custom-post-exporter'); ?></p>
                        
                        <h4><?php _e('Supported Field Types:', 'acf-custom-post-exporter'); ?></h4>
                        <ul>
                            <li><?php _e('Text, Textarea, Number', 'acf-custom-post-exporter'); ?></li>
                            <li><?php _e('Select, Checkbox, Radio', 'acf-custom-post-exporter'); ?></li>
                            <li><?php _e('Date, Time, DateTime', 'acf-custom-post-exporter'); ?></li>
                            <li><?php _e('Image, File, Gallery', 'acf-custom-post-exporter'); ?></li>
                            <li><?php _e('Post Object, Relationship', 'acf-custom-post-exporter'); ?></li>
                            <li><?php _e('Taxonomy, User', 'acf-custom-post-exporter'); ?></li>
                            <li><?php _e('And more...', 'acf-custom-post-exporter'); ?></li>
                        </ul>
                    </div>
                    
                    <div class="sidebar-box">
                        <h3><?php _e('Tips', 'acf-custom-post-exporter'); ?></h3>
                        <ul>
                            <li><?php _e('Large exports may take some time to complete', 'acf-custom-post-exporter'); ?></li>
                            <li><?php _e('Use the limit option for testing with smaller datasets', 'acf-custom-post-exporter'); ?></li>
                            <li><?php _e('CSV files are automatically cleaned up after 24 hours', 'acf-custom-post-exporter'); ?></li>
                        </ul>
                    </div>
                </div>
                
            </div>
        </div>
        <?php
    }
}
