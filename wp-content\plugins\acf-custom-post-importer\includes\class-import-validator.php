<?php
/**
 * Import Validator Class
 * 
 * Validates import data before processing
 */

if (!defined('ABSPATH')) {
    exit;
}

class ACF_CPT_Import_Validator {
    
    /**
     * Validate import data
     * 
     * @param array $data
     * @param string $post_type
     * @param array $field_mapping
     * @return array
     */
    public function validate_import_data($data, $post_type, $field_mapping) {
        $errors = array();
        $warnings = array();
        
        // Validate post type
        if (!post_type_exists($post_type)) {
            $errors[] = sprintf(__('Post type "%s" does not exist', 'acf-custom-post-importer'), $post_type);
            return array(
                'valid' => false,
                'errors' => $errors,
                'warnings' => $warnings
            );
        }
        
        // Get available fields
        $mapper = new ACF_CPT_Field_Mapper();
        $available_fields = $mapper->get_available_fields($post_type);
        $all_fields = array_merge($available_fields['standard'], $available_fields['acf']);
        
        // Validate field mapping
        $mapping_validation = $mapper->validate_mapping($field_mapping, $available_fields);
        if (!$mapping_validation['valid']) {
            $errors = array_merge($errors, $mapping_validation['errors']);
        }
        $warnings = array_merge($warnings, $mapping_validation['warnings']);
        
        // Validate sample data
        $sample_size = min(5, count($data));
        for ($i = 0; $i < $sample_size; $i++) {
            $row_errors = $this->validate_row_data($data[$i], $field_mapping, $all_fields, $i + 1);
            $errors = array_merge($errors, $row_errors);
        }
        
        return array(
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        );
    }
    
    /**
     * Validate a single row of data
     * 
     * @param array $row_data
     * @param array $field_mapping
     * @param array $all_fields
     * @param int $row_number
     * @return array
     */
    private function validate_row_data($row_data, $field_mapping, $all_fields, $row_number) {
        $errors = array();
        
        foreach ($field_mapping as $csv_column => $wp_field) {
            if (empty($wp_field) || !isset($row_data[$csv_column])) {
                continue;
            }
            
            $value = trim($row_data[$csv_column]);
            
            // Skip empty values for non-required fields
            if (empty($value)) {
                if (isset($all_fields[$wp_field]) && $all_fields[$wp_field]['required']) {
                    $errors[] = sprintf(__('Row %d: Required field "%s" is empty', 'acf-custom-post-importer'), 
                                      $row_number, $all_fields[$wp_field]['label']);
                }
                continue;
            }
            
            // Validate field value
            $field_errors = $this->validate_field_value($wp_field, $value, $all_fields, $row_number);
            $errors = array_merge($errors, $field_errors);
        }
        
        return $errors;
    }
    
    /**
     * Validate a field value
     * 
     * @param string $field_name
     * @param string $value
     * @param array $all_fields
     * @param int $row_number
     * @return array
     */
    private function validate_field_value($field_name, $value, $all_fields, $row_number) {
        $errors = array();
        
        if (!isset($all_fields[$field_name])) {
            return $errors;
        }
        
        $field = $all_fields[$field_name];
        
        switch ($field['type']) {
            case 'number':
                if (!is_numeric($value)) {
                    $errors[] = sprintf(__('Row %d: "%s" must be a number, got "%s"', 'acf-custom-post-importer'), 
                                      $row_number, $field['label'], $value);
                }
                break;
                
            case 'email':
                if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $errors[] = sprintf(__('Row %d: "%s" must be a valid email, got "%s"', 'acf-custom-post-importer'), 
                                      $row_number, $field['label'], $value);
                }
                break;
                
            case 'url':
                if (!filter_var($value, FILTER_VALIDATE_URL)) {
                    $errors[] = sprintf(__('Row %d: "%s" must be a valid URL, got "%s"', 'acf-custom-post-importer'), 
                                      $row_number, $field['label'], $value);
                }
                break;
                
            case 'date_picker':
                if (!$this->validate_date($value, 'Y-m-d')) {
                    $errors[] = sprintf(__('Row %d: "%s" must be in YYYY-MM-DD format, got "%s"', 'acf-custom-post-importer'), 
                                      $row_number, $field['label'], $value);
                }
                break;
                
            case 'date_time_picker':
                if (!$this->validate_date($value, 'Y-m-d H:i:s')) {
                    $errors[] = sprintf(__('Row %d: "%s" must be in YYYY-MM-DD HH:MM:SS format, got "%s"', 'acf-custom-post-importer'), 
                                      $row_number, $field['label'], $value);
                }
                break;
                
            case 'time_picker':
                if (!$this->validate_date($value, 'H:i:s')) {
                    $errors[] = sprintf(__('Row %d: "%s" must be in HH:MM:SS format, got "%s"', 'acf-custom-post-importer'), 
                                      $row_number, $field['label'], $value);
                }
                break;
                
            case 'select':
            case 'radio':
                if (isset($field['options']) && !empty($field['options'])) {
                    if (!in_array($value, array_keys($field['options'])) && !in_array($value, array_values($field['options']))) {
                        $errors[] = sprintf(__('Row %d: "%s" value "%s" is not in allowed options', 'acf-custom-post-importer'), 
                                          $row_number, $field['label'], $value);
                    }
                }
                break;
                
            case 'true_false':
                $valid_values = array('0', '1', 'true', 'false', 'yes', 'no', 'on', 'off');
                if (!in_array(strtolower($value), $valid_values)) {
                    $errors[] = sprintf(__('Row %d: "%s" must be true/false, yes/no, 1/0, got "%s"', 'acf-custom-post-importer'), 
                                      $row_number, $field['label'], $value);
                }
                break;
                
            case 'post_status':
                $valid_statuses = array('publish', 'draft', 'private', 'pending');
                if (!in_array($value, $valid_statuses)) {
                    $errors[] = sprintf(__('Row %d: "%s" must be one of: %s, got "%s"', 'acf-custom-post-importer'), 
                                      $row_number, $field['label'], implode(', ', $valid_statuses), $value);
                }
                break;
        }
        
        return $errors;
    }
    
    /**
     * Validate date format
     * 
     * @param string $date
     * @param string $format
     * @return bool
     */
    private function validate_date($date, $format) {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
    
    /**
     * Check for duplicate data
     * 
     * @param array $data
     * @param array $field_mapping
     * @return array
     */
    public function check_duplicates($data, $field_mapping) {
        $duplicates = array();
        $seen_values = array();
        
        // Check for ID duplicates if ID field is mapped
        if (in_array('ID', $field_mapping)) {
            $id_column = array_search('ID', $field_mapping);
            
            foreach ($data as $index => $row) {
                if (isset($row[$id_column]) && !empty($row[$id_column])) {
                    $id = trim($row[$id_column]);
                    if (isset($seen_values[$id])) {
                        $duplicates[] = sprintf(__('Duplicate ID "%s" found in rows %d and %d', 'acf-custom-post-importer'), 
                                              $id, $seen_values[$id] + 1, $index + 1);
                    } else {
                        $seen_values[$id] = $index;
                    }
                }
            }
        }
        
        return $duplicates;
    }
    
    /**
     * Estimate import time
     * 
     * @param int $total_rows
     * @param int $batch_size
     * @return array
     */
    public function estimate_import_time($total_rows, $batch_size = 50) {
        // Rough estimate: 1-2 seconds per batch
        $batches = ceil($total_rows / $batch_size);
        $estimated_seconds = $batches * 1.5;
        
        $hours = floor($estimated_seconds / 3600);
        $minutes = floor(($estimated_seconds % 3600) / 60);
        $seconds = $estimated_seconds % 60;
        
        $time_string = '';
        if ($hours > 0) {
            $time_string .= $hours . 'h ';
        }
        if ($minutes > 0) {
            $time_string .= $minutes . 'm ';
        }
        $time_string .= round($seconds) . 's';
        
        return array(
            'estimated_seconds' => $estimated_seconds,
            'estimated_time' => trim($time_string),
            'batches' => $batches
        );
    }
    
    /**
     * Get import recommendations
     * 
     * @param int $total_rows
     * @return array
     */
    public function get_import_recommendations($total_rows) {
        $recommendations = array();
        
        if ($total_rows > 1000) {
            $recommendations[] = __('Large import detected. Consider importing in smaller batches.', 'acf-custom-post-importer');
        }
        
        if ($total_rows > 5000) {
            $recommendations[] = __('Very large import. Recommend running during off-peak hours.', 'acf-custom-post-importer');
        }
        
        $recommendations[] = __('Backup your database before importing.', 'acf-custom-post-importer');
        $recommendations[] = __('Test with a small sample first.', 'acf-custom-post-importer');
        
        return $recommendations;
    }
}
