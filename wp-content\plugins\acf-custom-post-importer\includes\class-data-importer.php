<?php
/**
 * Data Importer Class
 * 
 * Handles the actual data import process
 */

if (!defined('ABSPATH')) {
    exit;
}

class ACF_CPT_Data_Importer {
    
    /**
     * Import data from CSV
     * 
     * @param array $params
     * @return array
     */
    public function import($params) {
        $file_path = sanitize_text_field($params['file_path']);
        $post_type = sanitize_text_field($params['post_type']);
        $field_mapping = $params['field_mapping'];
        $import_mode = sanitize_text_field($params['import_mode']); // create, update, or create_update
        $batch_size = isset($params['batch_size']) ? intval($params['batch_size']) : 50;
        $offset = isset($params['offset']) ? intval($params['offset']) : 0;
        
        // Validate post type
        if (!post_type_exists($post_type)) {
            return array(
                'success' => false,
                'message' => __('Invalid post type', 'acf-custom-post-importer')
            );
        }
        
        try {
            // Get CSV data
            $parser = new ACF_CPT_CSV_Parser();
            $csv_data = $parser->get_csv_data($file_path, $batch_size, $offset);
            
            if (empty($csv_data['data'])) {
                return array(
                    'success' => false,
                    'message' => __('No data to import', 'acf-custom-post-importer')
                );
            }
            
            // Process the data
            $results = $this->process_import_data($csv_data['data'], $post_type, $field_mapping, $import_mode);
            
            // Update progress
            $progress = array(
                'processed' => $csv_data['processed'],
                'total' => $csv_data['total'],
                'created' => $results['created'],
                'updated' => $results['updated'],
                'errors' => $results['errors'],
                'status' => $csv_data['processed'] >= $csv_data['total'] ? 'completed' : 'processing'
            );
            
            set_transient('acf_cpt_import_progress_' . get_current_user_id(), $progress, 3600);
            
            return array(
                'success' => true,
                'progress' => $progress,
                'continue' => $csv_data['processed'] < $csv_data['total'],
                'next_offset' => $csv_data['processed']
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => $e->getMessage()
            );
        }
    }
    
    /**
     * Process import data
     * 
     * @param array $data
     * @param string $post_type
     * @param array $field_mapping
     * @param string $import_mode
     * @return array
     */
    private function process_import_data($data, $post_type, $field_mapping, $import_mode) {
        $created = 0;
        $updated = 0;
        $errors = array();
        
        foreach ($data as $row_index => $row_data) {
            try {
                $result = $this->import_single_post($row_data, $post_type, $field_mapping, $import_mode);
                
                if ($result['success']) {
                    if ($result['action'] === 'created') {
                        $created++;
                    } else {
                        $updated++;
                    }
                } else {
                    $errors[] = sprintf(__('Row %d: %s', 'acf-custom-post-importer'), $row_index + 1, $result['message']);
                }
                
            } catch (Exception $e) {
                $errors[] = sprintf(__('Row %d: %s', 'acf-custom-post-importer'), $row_index + 1, $e->getMessage());
            }
        }
        
        return array(
            'created' => $created,
            'updated' => $updated,
            'errors' => $errors
        );
    }
    
    /**
     * Import a single post
     * 
     * @param array $row_data
     * @param string $post_type
     * @param array $field_mapping
     * @param string $import_mode
     * @return array
     */
    private function import_single_post($row_data, $post_type, $field_mapping, $import_mode) {
        // Prepare post data
        $post_data = array(
            'post_type' => $post_type,
            'post_status' => 'publish'
        );
        
        $acf_data = array();
        $taxonomy_data = array();
        $post_id = null;
        
        // Map CSV data to WordPress fields
        foreach ($field_mapping as $csv_column => $wp_field) {
            if (empty($wp_field) || !isset($row_data[$csv_column])) {
                continue;
            }
            
            $value = trim($row_data[$csv_column]);
            
            if (empty($value)) {
                continue;
            }
            
            // Handle different field types
            if ($wp_field === 'ID') {
                $post_id = intval($value);
            } elseif (strpos($wp_field, 'tax_') === 0) {
                $taxonomy = str_replace('tax_', '', $wp_field);
                $taxonomy_data[$taxonomy] = $this->parse_taxonomy_value($value);
            } elseif (in_array($wp_field, array('post_title', 'post_content', 'post_excerpt', 'post_status', 'post_date', 'post_author', 'menu_order'))) {
                $post_data[$wp_field] = $this->sanitize_post_field($wp_field, $value);
            } elseif ($wp_field === 'featured_image') {
                // Handle featured image separately after post creation
            } else {
                // ACF field
                $acf_data[$wp_field] = $this->parse_acf_value($wp_field, $value);
            }
        }
        
        // Determine if we're creating or updating
        $action = 'created';
        
        if ($post_id && $import_mode !== 'create') {
            // Check if post exists
            $existing_post = get_post($post_id);
            if ($existing_post && $existing_post->post_type === $post_type) {
                $post_data['ID'] = $post_id;
                $action = 'updated';
            } elseif ($import_mode === 'update') {
                return array(
                    'success' => false,
                    'message' => sprintf(__('Post ID %d not found', 'acf-custom-post-importer'), $post_id)
                );
            }
        }
        
        // Insert or update post
        if (isset($post_data['ID'])) {
            $result_id = wp_update_post($post_data, true);
        } else {
            $result_id = wp_insert_post($post_data, true);
        }
        
        if (is_wp_error($result_id)) {
            return array(
                'success' => false,
                'message' => $result_id->get_error_message()
            );
        }
        
        // Set featured image
        if (isset($field_mapping['featured_image'])) {
            $image_value = isset($row_data['featured_image']) ? trim($row_data['featured_image']) : '';
            if (!empty($image_value)) {
                $this->set_featured_image($result_id, $image_value);
            }
        }
        
        // Set ACF fields
        foreach ($acf_data as $field_name => $field_value) {
            update_field($field_name, $field_value, $result_id);
        }
        
        // Set taxonomies
        foreach ($taxonomy_data as $taxonomy => $terms) {
            wp_set_object_terms($result_id, $terms, $taxonomy);
        }
        
        return array(
            'success' => true,
            'action' => $action,
            'post_id' => $result_id
        );
    }
    
    /**
     * Sanitize post field value
     * 
     * @param string $field_name
     * @param string $value
     * @return mixed
     */
    private function sanitize_post_field($field_name, $value) {
        switch ($field_name) {
            case 'post_title':
                return sanitize_text_field($value);
                
            case 'post_content':
            case 'post_excerpt':
                return wp_kses_post($value);
                
            case 'post_status':
                $allowed_statuses = array('publish', 'draft', 'private', 'pending');
                return in_array($value, $allowed_statuses) ? $value : 'publish';
                
            case 'post_date':
                return date('Y-m-d H:i:s', strtotime($value));
                
            case 'post_author':
            case 'menu_order':
                return intval($value);
                
            default:
                return sanitize_text_field($value);
        }
    }
    
    /**
     * Parse taxonomy value
     * 
     * @param string $value
     * @return array
     */
    private function parse_taxonomy_value($value) {
        // Split by comma and clean up
        $terms = array_map('trim', explode(',', $value));
        return array_filter($terms);
    }
    
    /**
     * Parse ACF field value
     * 
     * @param string $field_name
     * @param string $value
     * @return mixed
     */
    private function parse_acf_value($field_name, $value) {
        // Get field object to determine type
        $field = get_field_object($field_name);
        
        if (!$field) {
            return $value;
        }
        
        switch ($field['type']) {
            case 'number':
                return is_numeric($value) ? floatval($value) : 0;
                
            case 'true_false':
                return in_array(strtolower($value), array('1', 'true', 'yes', 'on')) ? 1 : 0;
                
            case 'select':
            case 'radio':
                // Validate against choices
                if (isset($field['choices']) && !isset($field['choices'][$value])) {
                    // Try to find by label
                    foreach ($field['choices'] as $choice_value => $choice_label) {
                        if (strtolower($choice_label) === strtolower($value)) {
                            return $choice_value;
                        }
                    }
                }
                return $value;
                
            case 'checkbox':
                // Split by comma for multiple values
                $values = array_map('trim', explode(',', $value));
                return array_filter($values);
                
            case 'date_picker':
                return date('Y-m-d', strtotime($value));
                
            case 'date_time_picker':
                return date('Y-m-d H:i:s', strtotime($value));
                
            case 'time_picker':
                return date('H:i:s', strtotime($value));
                
            case 'image':
            case 'file':
                return $this->handle_media_field($value);
                
            case 'gallery':
                $urls = array_map('trim', explode(',', $value));
                $attachment_ids = array();
                foreach ($urls as $url) {
                    $attachment_id = $this->handle_media_field($url);
                    if ($attachment_id) {
                        $attachment_ids[] = $attachment_id;
                    }
                }
                return $attachment_ids;
                
            case 'post_object':
            case 'relationship':
                // Handle post IDs or titles
                $posts = array_map('trim', explode(',', $value));
                $post_ids = array();
                foreach ($posts as $post_ref) {
                    if (is_numeric($post_ref)) {
                        $post_ids[] = intval($post_ref);
                    } else {
                        // Try to find by title
                        $post = get_page_by_title($post_ref, OBJECT, $field['post_type']);
                        if ($post) {
                            $post_ids[] = $post->ID;
                        }
                    }
                }
                return $field['type'] === 'post_object' ? (count($post_ids) > 0 ? $post_ids[0] : null) : $post_ids;
                
            case 'user':
                if (is_numeric($value)) {
                    return intval($value);
                } else {
                    $user = get_user_by('login', $value);
                    return $user ? $user->ID : null;
                }
                
            case 'taxonomy':
                return $this->parse_taxonomy_value($value);
                
            default:
                return $value;
        }
    }
    
    /**
     * Handle media field (image/file)
     * 
     * @param string $value
     * @return int|null
     */
    private function handle_media_field($value) {
        if (is_numeric($value)) {
            // Assume it's an attachment ID
            return intval($value);
        } elseif (filter_var($value, FILTER_VALIDATE_URL)) {
            // It's a URL, try to import it
            return $this->import_media_from_url($value);
        }
        
        return null;
    }
    
    /**
     * Import media from URL
     * 
     * @param string $url
     * @return int|null
     */
    private function import_media_from_url($url) {
        // Check if attachment already exists
        $attachment_id = attachment_url_to_postid($url);
        if ($attachment_id) {
            return $attachment_id;
        }
        
        // Import the file
        require_once(ABSPATH . 'wp-admin/includes/media.php');
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        
        $attachment_id = media_sideload_image($url, 0, null, 'id');
        
        if (is_wp_error($attachment_id)) {
            return null;
        }
        
        return $attachment_id;
    }
    
    /**
     * Set featured image
     *
     * @param int $post_id
     * @param string $image_value
     */
    private function set_featured_image($post_id, $image_value) {
        $attachment_id = $this->handle_media_field($image_value);
        if ($attachment_id) {
            set_post_thumbnail($post_id, $attachment_id);
        }
    }
}
}
