/* WooCommerce ACF Product Exporter Admin Styles */

.wc-acf-exporter-container {
    max-width: 800px;
    margin: 20px 0;
}

.wc-acf-exporter-form {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.wc-acf-exporter-form h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.wc-acf-exporter-form .form-table th {
    width: 200px;
    padding: 15px 10px 15px 0;
}

.wc-acf-exporter-form .form-table td {
    padding: 15px 10px;
}

.wc-acf-exporter-form select[multiple] {
    height: 120px;
    width: 100%;
    max-width: 300px;
}

.wc-acf-exporter-form input[type="number"] {
    width: 100px;
}

.wc-acf-exporter-form .description {
    font-style: italic;
    color: #666;
    margin-top: 5px;
}

/* Progress Section */
.wc-acf-export-progress {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.wc-acf-export-progress h3 {
    margin-top: 0;
    margin-bottom: 15px;
}

.progress-bar {
    width: 100%;
    height: 30px;
    background-color: #f1f1f1;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 15px;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #00a0d2);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 15px;
}

.progress-text {
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.export-log {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    max-height: 200px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 12px;
    line-height: 1.4;
}

.export-log .log-entry {
    margin-bottom: 5px;
    color: #555;
}

.export-log .log-entry:last-child {
    margin-bottom: 0;
    font-weight: 600;
    color: #333;
}

/* Completion Section */
.wc-acf-export-complete {
    background: #fff;
    border: 1px solid #46b450;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #46b450;
}

.wc-acf-export-complete h3 {
    margin-top: 0;
    color: #46b450;
}

.wc-acf-export-complete p {
    margin-bottom: 15px;
}

.export-downloads {
    margin-bottom: 20px;
}

.import-info {
    background: #f0f8ff;
    border-left: 4px solid #0073aa;
    padding: 15px;
    margin-top: 15px;
    border-radius: 4px;
}

.import-info h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #0073aa;
}

.import-info ul {
    margin: 10px 0;
    padding-left: 20px;
}

.import-info li {
    margin-bottom: 5px;
}

/* Error Messages */
.export-error {
    margin-bottom: 20px;
}

/* Button Styles */
#start-export {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
    font-size: 14px;
    padding: 8px 16px;
    margin-right: 10px;
}

#start-export:hover {
    background: #005a87;
    border-color: #005a87;
}

#start-export:disabled {
    background: #ccc;
    border-color: #ccc;
    cursor: not-allowed;
}

#reset-export {
    margin-left: 10px;
}

#download-export {
    background: #46b450;
    border-color: #46b450;
    color: #fff;
    text-decoration: none;
    display: inline-block;
    padding: 8px 16px;
    border-radius: 3px;
    font-size: 14px;
}

#download-export:hover {
    background: #3e9b47;
    border-color: #3e9b47;
    color: #fff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .wc-acf-exporter-container {
        margin: 10px 0;
    }
    
    .wc-acf-exporter-form,
    .wc-acf-export-progress,
    .wc-acf-export-complete {
        padding: 15px;
    }
    
    .wc-acf-exporter-form .form-table th {
        width: auto;
        display: block;
        padding-bottom: 5px;
    }
    
    .wc-acf-exporter-form .form-table td {
        display: block;
        padding-top: 0;
    }
    
    .wc-acf-exporter-form select[multiple] {
        max-width: 100%;
    }
}

/* Loading Animation */
.wc-acf-export-progress .progress-fill {
    position: relative;
    overflow: hidden;
}

.wc-acf-export-progress .progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, .2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, .2) 50%,
        rgba(255, 255, 255, .2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 50px 50px;
    animation: move 2s linear infinite;
}

@keyframes move {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 50px 50px;
    }
}

/* Accessibility */
.wc-acf-exporter-form label {
    cursor: pointer;
}

.wc-acf-exporter-form input:focus,
.wc-acf-exporter-form select:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .wc-acf-exporter-form,
    .wc-acf-export-progress {
        border: none;
        box-shadow: none;
    }
    
    .progress-bar {
        border: 1px solid #000;
    }
    
    .export-log {
        border: 1px solid #000;
        background: #fff;
    }
}
