jQuery(document).ready(function($) {
    'use strict';

    console.log('ACF Importer JavaScript loaded');

    // Initialize the importer
    var ACFCPTImporter = {
        
        csvData: null,
        availableFields: null,
        currentMapping: {},
        
        init: function() {
            this.bindEvents();
            // Don't load post types immediately - wait until CSV is uploaded
        },
        
        bindEvents: function() {
            // File upload events
            $('#csv-file').on('change', this.onFileSelect);
            $('#browse-btn').on('click', function() { $('#csv-file').click(); });
            $('#csv-upload-form').on('submit', this.uploadCSV);
            
            // Post type selection
            $('#import-post-type').on('change', this.onPostTypeChange);
            
            // Field mapping events
            $('#auto-map-btn').on('click', this.autoMapFields);
            $('#clear-mapping-btn').on('click', this.clearMapping);
            $(document).on('change', '.field-mapping-select', this.onFieldMappingChange);
            
            // Import events
            $('#validate-btn').on('click', this.validateData);
            $('#start-import-btn').on('click', this.startImport);
            $('#reset-import-btn, #new-import-btn').on('click', this.resetForm);
            
            // Batch size change
            $('#batch-size').on('change', this.updateSummary);
        },
        
        loadPostTypes: function() {
            $.ajax({
                url: acf_cpt_importer.ajax_url,
                type: 'POST',
                data: {
                    action: 'acf_cpt_get_post_types',
                    nonce: acf_cpt_importer.nonce
                },
                success: function(response) {
                    console.log('Post types response:', response);
                    if (response.success) {
                        var $select = $('#import-post-type');
                        $select.empty().append('<option value="">' + acf_cpt_importer.strings.select_post_type + '</option>');

                        $.each(response.data, function(key, postType) {
                            $select.append('<option value="' + postType.name + '">' +
                                         postType.label + ' (' + postType.count + ')</option>');
                        });
                    } else {
                        console.error('Failed to load post types:', response);
                        alert('Failed to load post types: ' + (response.data ? response.data.message : 'Unknown error'));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error loading post types:', xhr, status, error);
                    alert('AJAX error loading post types: ' + error);
                }
            });
        },
        
        onFileSelect: function() {
            var file = this.files[0];
            var $uploadBtn = $('#upload-btn');
            
            if (file) {
                // Validate file
                if (file.type !== 'text/csv' && !file.name.toLowerCase().endsWith('.csv')) {
                    alert('Please select a CSV file.');
                    $(this).val('');
                    $uploadBtn.prop('disabled', true);
                    return;
                }
                
                if (file.size > acf_cpt_importer.max_file_size) {
                    alert(acf_cpt_importer.strings.file_too_large);
                    $(this).val('');
                    $uploadBtn.prop('disabled', true);
                    return;
                }
                
                $uploadBtn.prop('disabled', false);
                $('.upload-box p').text(file.name + ' (' + ACFCPTImporter.formatFileSize(file.size) + ')');
            } else {
                $uploadBtn.prop('disabled', true);
            }
        },
        
        uploadCSV: function(e) {
            e.preventDefault();
            
            var formData = new FormData();
            formData.append('csv_file', $('#csv-file')[0].files[0]);
            formData.append('action', 'acf_cpt_upload_csv');
            formData.append('nonce', acf_cpt_importer.nonce);
            
            $.ajax({
                url: acf_cpt_importer.ajax_url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                beforeSend: function() {
                    $('#upload-btn').prop('disabled', true).text(acf_cpt_importer.strings.uploading);
                },
                success: function(response) {
                    if (response.success) {
                        ACFCPTImporter.parseCSV(response.data.file_path);
                    } else {
                        alert(response.data.message || acf_cpt_importer.strings.import_error);
                    }
                },
                error: function() {
                    alert(acf_cpt_importer.strings.import_error);
                },
                complete: function() {
                    $('#upload-btn').prop('disabled', false).text('Upload and Parse');
                }
            });
        },
        
        parseCSV: function(filePath) {
            $.ajax({
                url: acf_cpt_importer.ajax_url,
                type: 'POST',
                data: {
                    action: 'acf_cpt_parse_csv',
                    file_path: filePath,
                    nonce: acf_cpt_importer.nonce
                },
                beforeSend: function() {
                    $('#upload-btn').text(acf_cpt_importer.strings.parsing);
                },
                success: function(response) {
                    if (response.success) {
                        ACFCPTImporter.csvData = response.data;
                        ACFCPTImporter.showCSVPreview();
                        ACFCPTImporter.loadPostTypes(); // Load post types after CSV is parsed
                        $('#step-2').show();
                    } else {
                        alert(response.data.message || acf_cpt_importer.strings.import_error);
                    }
                },
                error: function() {
                    alert(acf_cpt_importer.strings.import_error);
                },
                complete: function() {
                    $('#upload-btn').text('Upload and Parse');
                }
            });
        },
        
        showCSVPreview: function() {
            var data = this.csvData;
            
            $('#total-rows').text(data.total_rows);
            $('#total-columns').text(data.headers.length);
            
            // Build preview table
            var $table = $('#preview-table');
            var $thead = $table.find('thead');
            var $tbody = $table.find('tbody');
            
            // Headers
            var headerRow = '<tr>';
            $.each(data.headers, function(index, header) {
                headerRow += '<th>' + header + '</th>';
            });
            headerRow += '</tr>';
            $thead.html(headerRow);
            
            // Preview data (first 5 rows)
            $tbody.empty();
            $.each(data.preview_data, function(rowIndex, row) {
                var dataRow = '<tr>';
                $.each(data.headers, function(colIndex, header) {
                    var cellValue = row[header] || '';
                    if (cellValue.length > 50) {
                        cellValue = cellValue.substring(0, 50) + '...';
                    }
                    dataRow += '<td>' + cellValue + '</td>';
                });
                dataRow += '</tr>';
                $tbody.append(dataRow);
            });
            
            $('#csv-preview').show();
        },
        
        onPostTypeChange: function() {
            var postType = $(this).val();
            
            if (postType) {
                ACFCPTImporter.loadAvailableFields(postType);
            } else {
                $('#field-mapping').hide();
            }
        },
        
        loadAvailableFields: function(postType) {
            $.ajax({
                url: acf_cpt_importer.ajax_url,
                type: 'POST',
                data: {
                    action: 'acf_cpt_get_acf_fields',
                    post_type: postType,
                    nonce: acf_cpt_importer.nonce
                },
                success: function(response) {
                    if (response.success) {
                        ACFCPTImporter.availableFields = response.data;
                        ACFCPTImporter.buildFieldMapping();
                        $('#field-mapping').show();
                        $('#step-3').show();
                    } else {
                        alert(response.data.message || acf_cpt_importer.strings.import_error);
                    }
                }
            });
        },
        
        buildFieldMapping: function() {
            var $tbody = $('#mapping-table tbody');
            $tbody.empty();
            
            var allFields = $.extend({}, this.availableFields.standard, this.availableFields.acf);
            
            // Build field options
            var fieldOptions = '<option value="">' + 'Do not import' + '</option>';
            
            // Standard fields
            fieldOptions += '<optgroup label="Standard Fields">';
            $.each(this.availableFields.standard, function(key, field) {
                var required = field.required ? ' *' : '';
                fieldOptions += '<option value="' + field.name + '">' + field.label + required + '</option>';
            });
            fieldOptions += '</optgroup>';
            
            // ACF fields
            if (Object.keys(this.availableFields.acf).length > 0) {
                fieldOptions += '<optgroup label="ACF Fields">';
                $.each(this.availableFields.acf, function(key, field) {
                    var required = field.required ? ' *' : '';
                    fieldOptions += '<option value="' + field.name + '">' + field.label + required + '</option>';
                });
                fieldOptions += '</optgroup>';
            }
            
            // Create mapping rows
            $.each(this.csvData.headers, function(index, header) {
                var sampleData = '';
                if (ACFCPTImporter.csvData.preview_data.length > 0) {
                    sampleData = ACFCPTImporter.csvData.preview_data[0][header] || '';
                    if (sampleData.length > 30) {
                        sampleData = sampleData.substring(0, 30) + '...';
                    }
                }
                
                var row = '<tr>' +
                         '<td><strong>' + header + '</strong></td>' +
                         '<td>' + sampleData + '</td>' +
                         '<td><select class="field-mapping-select" data-csv-column="' + header + '">' + fieldOptions + '</select></td>' +
                         '<td class="field-type-info">-</td>' +
                         '</tr>';
                $tbody.append(row);
            });
        },
        
        onFieldMappingChange: function() {
            var $select = $(this);
            var csvColumn = $select.data('csv-column');
            var fieldName = $select.val();
            var $typeCell = $select.closest('tr').find('.field-type-info');
            
            if (fieldName) {
                ACFCPTImporter.currentMapping[csvColumn] = fieldName;
                
                // Show field type info
                var allFields = $.extend({}, ACFCPTImporter.availableFields.standard, ACFCPTImporter.availableFields.acf);
                if (allFields[fieldName]) {
                    var field = allFields[fieldName];
                    var typeInfo = field.type;
                    if (field.description) {
                        typeInfo += '<br><small>' + field.description + '</small>';
                    }
                    $typeCell.html(typeInfo);
                }
            } else {
                delete ACFCPTImporter.currentMapping[csvColumn];
                $typeCell.text('-');
            }
        },
        
        autoMapFields: function() {
            // Simple auto-mapping based on field names
            var allFields = $.extend({}, ACFCPTImporter.availableFields.standard, ACFCPTImporter.availableFields.acf);
            
            $.each(ACFCPTImporter.csvData.headers, function(index, header) {
                var $select = $('.field-mapping-select[data-csv-column="' + header + '"]');
                var headerLower = header.toLowerCase().replace(/[^a-z0-9]/g, '');
                
                // Direct match
                if (allFields[header]) {
                    $select.val(header).trigger('change');
                    return;
                }
                
                // Fuzzy match
                $.each(allFields, function(fieldName, field) {
                    var fieldNameLower = fieldName.toLowerCase().replace(/[^a-z0-9]/g, '');
                    var fieldLabelLower = field.label.toLowerCase().replace(/[^a-z0-9]/g, '');
                    
                    if (headerLower === fieldNameLower || headerLower === fieldLabelLower) {
                        $select.val(fieldName).trigger('change');
                        return false;
                    }
                });
            });
        },
        
        clearMapping: function() {
            $('.field-mapping-select').val('').trigger('change');
            ACFCPTImporter.currentMapping = {};
        },
        
        validateData: function() {
            var postType = $('#import-post-type').val();
            var importMode = $('#import-mode').val();
            var batchSize = parseInt($('#batch-size').val());
            
            if (!postType) {
                alert(acf_cpt_importer.strings.select_post_type);
                return;
            }
            
            if (Object.keys(ACFCPTImporter.currentMapping).length === 0) {
                alert(acf_cpt_importer.strings.map_required_fields);
                return;
            }
            
            // Show validation results
            $('#validation-results').show();
            $('#summary-total').text(ACFCPTImporter.csvData.total_rows);
            $('#summary-batch').text(batchSize);
            
            // Estimate time (rough calculation)
            var estimatedMinutes = Math.ceil(ACFCPTImporter.csvData.total_rows / batchSize / 2);
            $('#summary-time').text(estimatedMinutes + ' minutes');
            
            $('#start-import-btn').prop('disabled', false);
        },
        
        startImport: function() {
            if (!confirm(acf_cpt_importer.strings.confirm_import)) {
                return;
            }
            
            ACFCPTImporter.runImport(0);
        },
        
        runImport: function(offset) {
            var postType = $('#import-post-type').val();
            var importMode = $('#import-mode').val();
            var batchSize = parseInt($('#batch-size').val());
            
            $('#step-3').hide();
            $('#import-progress').show();
            
            $.ajax({
                url: acf_cpt_importer.ajax_url,
                type: 'POST',
                data: {
                    action: 'acf_cpt_import_data',
                    file_path: ACFCPTImporter.csvData.file_path,
                    post_type: postType,
                    field_mapping: ACFCPTImporter.currentMapping,
                    import_mode: importMode,
                    batch_size: batchSize,
                    offset: offset,
                    nonce: acf_cpt_importer.nonce
                },
                success: function(response) {
                    if (response.success) {
                        ACFCPTImporter.updateProgress(response.data.progress);
                        
                        if (response.data.continue) {
                            // Continue with next batch
                            setTimeout(function() {
                                ACFCPTImporter.runImport(response.data.next_offset);
                            }, 1000);
                        } else {
                            // Import complete
                            ACFCPTImporter.showResults(response.data.progress);
                        }
                    } else {
                        ACFCPTImporter.showError(response.data.message);
                    }
                },
                error: function() {
                    ACFCPTImporter.showError(acf_cpt_importer.strings.import_error);
                }
            });
        },
        
        updateProgress: function(progress) {
            var percentage = Math.round((progress.processed / progress.total) * 100);
            
            $('.progress-fill').css('width', percentage + '%');
            $('#processed-count').text(progress.processed);
            $('#created-count').text(progress.created);
            $('#updated-count').text(progress.updated);
            $('#error-count').text(progress.errors.length);
            
            $('.progress-text').text('Processing... ' + percentage + '% complete');
        },
        
        showResults: function(progress) {
            $('#import-progress').hide();
            $('#import-results').show();
            
            var summary = '<p><strong>Import Summary:</strong></p>' +
                         '<ul>' +
                         '<li>Total processed: ' + progress.processed + '</li>' +
                         '<li>Created: ' + progress.created + '</li>' +
                         '<li>Updated: ' + progress.updated + '</li>' +
                         '<li>Errors: ' + progress.errors.length + '</li>' +
                         '</ul>';
            
            if (progress.errors.length > 0) {
                $('#import-errors').show();
                $('#error-details').html(summary + '<h4>Errors:</h4><ul><li>' + progress.errors.join('</li><li>') + '</li></ul>');
            } else {
                $('#import-success').show();
                $('#final-summary').html(summary);
            }
        },
        
        showError: function(message) {
            $('#import-progress').hide();
            $('#import-results').show();
            $('#import-errors').show();
            $('#error-details').html('<p>' + message + '</p>');
        },
        
        resetForm: function() {
            // Reset to initial state
            $('#step-2, #step-3, #import-progress, #import-results').hide();
            $('#step-1').show();
            
            // Clear form data
            $('#csv-file').val('');
            $('#import-post-type').val('');
            $('#upload-btn').prop('disabled', true);
            $('.upload-box p').text('Drag and drop your CSV file here, or click to browse');
            
            // Clear data
            ACFCPTImporter.csvData = null;
            ACFCPTImporter.availableFields = null;
            ACFCPTImporter.currentMapping = {};
        },
        
        updateSummary: function() {
            var batchSize = parseInt($('#batch-size').val());
            $('#summary-batch').text(batchSize);
            
            if (ACFCPTImporter.csvData) {
                var estimatedMinutes = Math.ceil(ACFCPTImporter.csvData.total_rows / batchSize / 2);
                $('#summary-time').text(estimatedMinutes + ' minutes');
            }
        },
        
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 Bytes';
            var k = 1024;
            var sizes = ['Bytes', 'KB', 'MB', 'GB'];
            var i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    };
    
    // Initialize the importer
    ACFCPTImporter.init();

    // Make ACFCPTImporter available globally for debugging
    window.ACFCPTImporter = ACFCPTImporter;
});

// Debug function - can be called from browser console
window.debugLoadPostTypes = function() {
    if (window.ACFCPTImporter) {
        window.ACFCPTImporter.loadPostTypes();
    } else {
        console.error('ACFCPTImporter not available');
    }
};
