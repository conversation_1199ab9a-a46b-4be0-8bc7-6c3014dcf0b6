<?php
/**
 * CSV Parser Class
 * 
 * Handles CSV file upload and parsing
 */

if (!defined('ABSPATH')) {
    exit;
}

class ACF_CPT_CSV_Parser {
    
    /**
     * Handle CSV file upload
     * 
     * @return array
     */
    public function handle_upload() {
        if (!isset($_FILES['csv_file'])) {
            return array(
                'success' => false,
                'message' => __('No file uploaded', 'acf-custom-post-importer')
            );
        }
        
        $file = $_FILES['csv_file'];
        
        // Validate file
        $validation = $this->validate_upload($file);
        if (!$validation['valid']) {
            return array(
                'success' => false,
                'message' => $validation['message']
            );
        }
        
        // Move file to import directory
        $upload_dir = wp_upload_dir();
        $import_dir = $upload_dir['basedir'] . '/acf-cpt-imports/';
        
        if (!file_exists($import_dir)) {
            wp_mkdir_p($import_dir);
        }
        
        $filename = 'import_' . time() . '_' . sanitize_file_name($file['name']);
        $file_path = $import_dir . $filename;
        
        if (!move_uploaded_file($file['tmp_name'], $file_path)) {
            return array(
                'success' => false,
                'message' => __('Failed to save uploaded file', 'acf-custom-post-importer')
            );
        }
        
        return array(
            'success' => true,
            'file_path' => $file_path,
            'filename' => $filename,
            'file_size' => filesize($file_path)
        );
    }
    
    /**
     * Validate uploaded file
     * 
     * @param array $file
     * @return array
     */
    private function validate_upload($file) {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return array(
                'valid' => false,
                'message' => $this->get_upload_error_message($file['error'])
            );
        }
        
        // Check file size (max 10MB)
        $max_size = 10 * 1024 * 1024; // 10MB
        if ($file['size'] > $max_size) {
            return array(
                'valid' => false,
                'message' => __('File size exceeds 10MB limit', 'acf-custom-post-importer')
            );
        }
        
        // Check file type
        $allowed_types = array('text/csv', 'application/csv', 'text/plain');
        $file_type = $file['type'];
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (!in_array($file_type, $allowed_types) && $file_extension !== 'csv') {
            return array(
                'valid' => false,
                'message' => __('Only CSV files are allowed', 'acf-custom-post-importer')
            );
        }
        
        return array('valid' => true);
    }
    
    /**
     * Get upload error message
     * 
     * @param int $error_code
     * @return string
     */
    private function get_upload_error_message($error_code) {
        switch ($error_code) {
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                return __('File size exceeds limit', 'acf-custom-post-importer');
            case UPLOAD_ERR_PARTIAL:
                return __('File was only partially uploaded', 'acf-custom-post-importer');
            case UPLOAD_ERR_NO_FILE:
                return __('No file was uploaded', 'acf-custom-post-importer');
            case UPLOAD_ERR_NO_TMP_DIR:
                return __('Missing temporary folder', 'acf-custom-post-importer');
            case UPLOAD_ERR_CANT_WRITE:
                return __('Failed to write file to disk', 'acf-custom-post-importer');
            case UPLOAD_ERR_EXTENSION:
                return __('File upload stopped by extension', 'acf-custom-post-importer');
            default:
                return __('Unknown upload error', 'acf-custom-post-importer');
        }
    }
    
    /**
     * Parse CSV file
     * 
     * @param string $file_path
     * @return array
     */
    public function parse_csv($file_path) {
        if (!file_exists($file_path)) {
            throw new Exception(__('CSV file not found', 'acf-custom-post-importer'));
        }
        
        $handle = fopen($file_path, 'r');
        if (!$handle) {
            throw new Exception(__('Cannot open CSV file', 'acf-custom-post-importer'));
        }
        
        $data = array();
        $headers = array();
        $row_count = 0;
        
        while (($row = fgetcsv($handle, 0, ',')) !== FALSE) {
            if ($row_count === 0) {
                // First row contains headers
                $headers = array_map('trim', $row);
            } else {
                // Data rows
                if (count($row) === count($headers)) {
                    $data[] = array_combine($headers, array_map('trim', $row));
                }
            }
            $row_count++;
            
            // Limit preview to first 100 rows for performance
            if ($row_count > 100) {
                break;
            }
        }
        
        fclose($handle);
        
        // Get total row count
        $total_rows = $this->count_csv_rows($file_path) - 1; // Subtract header row
        
        return array(
            'headers' => $headers,
            'preview_data' => array_slice($data, 0, 5), // First 5 rows for preview
            'total_rows' => $total_rows,
            'file_path' => $file_path
        );
    }
    
    /**
     * Count total rows in CSV file
     * 
     * @param string $file_path
     * @return int
     */
    private function count_csv_rows($file_path) {
        $handle = fopen($file_path, 'r');
        if (!$handle) {
            return 0;
        }
        
        $count = 0;
        while (fgetcsv($handle) !== FALSE) {
            $count++;
        }
        
        fclose($handle);
        return $count;
    }
    
    /**
     * Get all data from CSV file (for actual import)
     * 
     * @param string $file_path
     * @param int $batch_size
     * @param int $offset
     * @return array
     */
    public function get_csv_data($file_path, $batch_size = 50, $offset = 0) {
        if (!file_exists($file_path)) {
            throw new Exception(__('CSV file not found', 'acf-custom-post-importer'));
        }
        
        $handle = fopen($file_path, 'r');
        if (!$handle) {
            throw new Exception(__('Cannot open CSV file', 'acf-custom-post-importer'));
        }
        
        $headers = array();
        $data = array();
        $row_count = 0;
        $data_count = 0;
        
        while (($row = fgetcsv($handle, 0, ',')) !== FALSE) {
            if ($row_count === 0) {
                // First row contains headers
                $headers = array_map('trim', $row);
            } else {
                // Skip rows until we reach the offset
                if ($data_count >= $offset) {
                    if (count($row) === count($headers)) {
                        $data[] = array_combine($headers, array_map('trim', $row));
                    }
                    
                    // Stop when we've collected enough rows for this batch
                    if (count($data) >= $batch_size) {
                        break;
                    }
                }
                $data_count++;
            }
            $row_count++;
        }
        
        fclose($handle);
        
        return array(
            'headers' => $headers,
            'data' => $data,
            'processed' => $offset + count($data),
            'total' => $this->count_csv_rows($file_path) - 1
        );
    }
    
    /**
     * Detect post type from CSV headers
     * 
     * @param array $headers
     * @return string|null
     */
    public function detect_post_type($headers) {
        // Look for common patterns in headers to suggest post type
        $header_string = strtolower(implode(' ', $headers));
        
        if (strpos($header_string, 'product') !== false || strpos($header_string, 'sku') !== false) {
            return 'product';
        }
        
        if (strpos($header_string, 'certificate') !== false) {
            return 'certificates';
        }
        
        if (strpos($header_string, 'news') !== false || strpos($header_string, 'newsroom') !== false) {
            return 'newsroom';
        }
        
        if (strpos($header_string, 'history') !== false || strpos($header_string, 'histories') !== false) {
            return 'Histories';
        }
        
        return null;
    }
    
    /**
     * Clean up uploaded file
     * 
     * @param string $file_path
     */
    public function cleanup_file($file_path) {
        if (file_exists($file_path)) {
            unlink($file_path);
        }
    }
}
