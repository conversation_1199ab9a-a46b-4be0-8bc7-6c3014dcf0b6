<?php
/**
 * <PERSON><PERSON> Handler class for WooCommerce ACF Product Exporter
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WC_ACF_Product_ACF_Handler {
    
    /**
     * Get all ACF fields for a product
     */
    public function get_product_acf_fields($product_id) {
        if (!function_exists('get_fields')) {
            return array();
        }
        
        $acf_data = array();
        $fields = get_fields($product_id);
        
        if (empty($fields)) {
            return $acf_data;
        }
        
        foreach ($fields as $field_name => $field_value) {
            $field_object = get_field_object($field_name, $product_id);
            
            if (!$field_object) {
                continue;
            }
            
            $formatted_value = $this->format_acf_field_value($field_value, $field_object);
            $acf_data['acf_' . $field_name] = $formatted_value;
            
            // Store field type for import reference
            $acf_data['acf_' . $field_name . '_type'] = $field_object['type'];
            
            // Store additional field configuration if needed
            if (in_array($field_object['type'], array('select', 'checkbox', 'radio'))) {
                $acf_data['acf_' . $field_name . '_choices'] = $this->format_field_choices($field_object);
            }
        }
        
        return $acf_data;
    }
    
    /**
     * Get ACF taxonomies for a product
     */
    public function get_product_acf_taxonomies($product_id) {
        if (!function_exists('get_fields')) {
            return array();
        }
        
        $acf_taxonomy_data = array();
        
        // Get all ACF field groups
        $field_groups = acf_get_field_groups();
        
        foreach ($field_groups as $field_group) {
            // Check if this field group applies to products
            if (!$this->field_group_applies_to_product($field_group)) {
                continue;
            }
            
            $fields = acf_get_fields($field_group['key']);
            
            foreach ($fields as $field) {
                if ($field['type'] === 'taxonomy') {
                    $taxonomy_data = $this->get_taxonomy_field_data($product_id, $field);
                    if (!empty($taxonomy_data)) {
                        $acf_taxonomy_data = array_merge($acf_taxonomy_data, $taxonomy_data);
                    }
                }
            }
        }
        
        // Get custom ACF taxonomies
        $custom_taxonomies = $this->get_acf_custom_taxonomies();
        foreach ($custom_taxonomies as $taxonomy) {
            $terms = wp_get_post_terms($product_id, $taxonomy);
            if (!empty($terms) && !is_wp_error($terms)) {
                $term_names = array();
                $term_ids = array();
                
                foreach ($terms as $term) {
                    $term_names[] = $term->name;
                    $term_ids[] = $term->term_id;
                    
                    // Get ACF fields for this term
                    $term_acf_fields = $this->get_term_acf_fields($term->term_id, $taxonomy);
                    if (!empty($term_acf_fields)) {
                        foreach ($term_acf_fields as $field_key => $field_value) {
                            $acf_taxonomy_data['acf_term_' . $taxonomy . '_' . $term->term_id . '_' . $field_key] = $field_value;
                        }
                    }
                }
                
                $acf_taxonomy_data['acf_taxonomy_' . $taxonomy] = implode(',', $term_names);
                $acf_taxonomy_data['acf_taxonomy_' . $taxonomy . '_ids'] = implode(',', $term_ids);
            }
        }
        
        return $acf_taxonomy_data;
    }
    
    /**
     * Format ACF field value for export
     */
    private function format_acf_field_value($value, $field_object) {
        if (empty($value)) {
            return '';
        }
        
        switch ($field_object['type']) {
            case 'image':
                if (is_array($value)) {
                    return $value['url'];
                } elseif (is_numeric($value)) {
                    return wp_get_attachment_url($value);
                }
                return $value;
                
            case 'gallery':
                if (is_array($value)) {
                    $urls = array();
                    foreach ($value as $image) {
                        if (is_array($image) && isset($image['url'])) {
                            $urls[] = $image['url'];
                        } elseif (is_numeric($image)) {
                            $urls[] = wp_get_attachment_url($image);
                        }
                    }
                    return implode(',', $urls);
                }
                return $value;
                
            case 'file':
                if (is_array($value)) {
                    return $value['url'];
                } elseif (is_numeric($value)) {
                    return wp_get_attachment_url($value);
                }
                return $value;
                
            case 'post_object':
            case 'page_link':
                if (is_object($value)) {
                    return $value->ID;
                } elseif (is_array($value)) {
                    $ids = array();
                    foreach ($value as $post) {
                        if (is_object($post)) {
                            $ids[] = $post->ID;
                        } elseif (is_numeric($post)) {
                            $ids[] = $post;
                        }
                    }
                    return implode(',', $ids);
                }
                return $value;
                
            case 'relationship':
                if (is_array($value)) {
                    $ids = array();
                    foreach ($value as $post) {
                        if (is_object($post)) {
                            $ids[] = $post->ID;
                        } elseif (is_numeric($post)) {
                            $ids[] = $post;
                        }
                    }
                    return implode(',', $ids);
                }
                return $value;
                
            case 'user':
                if (is_object($value)) {
                    return $value->ID;
                } elseif (is_array($value)) {
                    $ids = array();
                    foreach ($value as $user) {
                        if (is_object($user)) {
                            $ids[] = $user->ID;
                        } elseif (is_numeric($user)) {
                            $ids[] = $user;
                        }
                    }
                    return implode(',', $ids);
                }
                return $value;
                
            case 'taxonomy':
                if (is_array($value)) {
                    $term_names = array();
                    foreach ($value as $term) {
                        if (is_object($term)) {
                            $term_names[] = $term->name;
                        } elseif (is_numeric($term)) {
                            $term_obj = get_term($term);
                            if ($term_obj && !is_wp_error($term_obj)) {
                                $term_names[] = $term_obj->name;
                            }
                        }
                    }
                    return implode(',', $term_names);
                }
                return $value;
                
            case 'select':
            case 'checkbox':
            case 'radio':
                if (is_array($value)) {
                    return implode(',', $value);
                }
                return $value;
                
            case 'true_false':
                return $value ? '1' : '0';
                
            case 'date_picker':
            case 'date_time_picker':
            case 'time_picker':
                return $value;
                
            case 'repeater':
                return $this->format_repeater_field($value);
                
            case 'flexible_content':
                return $this->format_flexible_content_field($value);
                
            case 'group':
                return $this->format_group_field($value);
                
            default:
                if (is_array($value)) {
                    return json_encode($value);
                }
                return $value;
        }
    }
    
    /**
     * Format repeater field for export
     */
    private function format_repeater_field($value) {
        if (!is_array($value)) {
            return '';
        }
        
        $formatted_rows = array();
        foreach ($value as $row) {
            if (is_array($row)) {
                $formatted_row = array();
                foreach ($row as $sub_field_name => $sub_field_value) {
                    $formatted_row[] = $sub_field_name . ':' . $sub_field_value;
                }
                $formatted_rows[] = implode('|', $formatted_row);
            }
        }
        
        return implode(';', $formatted_rows);
    }
    
    /**
     * Format flexible content field for export
     */
    private function format_flexible_content_field($value) {
        if (!is_array($value)) {
            return '';
        }
        
        return json_encode($value);
    }
    
    /**
     * Format group field for export
     */
    private function format_group_field($value) {
        if (!is_array($value)) {
            return '';
        }
        
        $formatted_fields = array();
        foreach ($value as $sub_field_name => $sub_field_value) {
            $formatted_fields[] = $sub_field_name . ':' . $sub_field_value;
        }
        
        return implode('|', $formatted_fields);
    }
    
    /**
     * Format field choices for export
     */
    private function format_field_choices($field_object) {
        if (empty($field_object['choices'])) {
            return '';
        }
        
        $choices = array();
        foreach ($field_object['choices'] as $value => $label) {
            $choices[] = $value . ':' . $label;
        }
        
        return implode('|', $choices);
    }
    
    /**
     * Check if field group applies to products
     */
    private function field_group_applies_to_product($field_group) {
        if (empty($field_group['location'])) {
            return false;
        }
        
        foreach ($field_group['location'] as $location_group) {
            foreach ($location_group as $location_rule) {
                if ($location_rule['param'] === 'post_type' && $location_rule['value'] === 'product') {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Get taxonomy field data
     */
    private function get_taxonomy_field_data($product_id, $field) {
        $value = get_field($field['name'], $product_id);
        
        if (empty($value)) {
            return array();
        }
        
        $data = array();
        $formatted_value = $this->format_acf_field_value($value, $field);
        
        $data['acf_taxonomy_' . $field['name']] = $formatted_value;
        $data['acf_taxonomy_' . $field['name'] . '_type'] = $field['type'];
        $data['acf_taxonomy_' . $field['name'] . '_taxonomy'] = $field['taxonomy'];
        
        return $data;
    }
    
    /**
     * Get ACF custom taxonomies
     */
    private function get_acf_custom_taxonomies() {
        $custom_taxonomies = array();
        $taxonomies = get_taxonomies(array('public' => true), 'objects');
        
        foreach ($taxonomies as $taxonomy) {
            // Check if this is a custom taxonomy (not built-in WooCommerce ones)
            if (!in_array($taxonomy->name, array('product_cat', 'product_tag', 'product_shipping_class'))) {
                // Check if it's associated with products
                if (in_array('product', $taxonomy->object_type)) {
                    $custom_taxonomies[] = $taxonomy->name;
                }
            }
        }
        
        return $custom_taxonomies;
    }
    
    /**
     * Get ACF fields for a taxonomy term
     */
    private function get_term_acf_fields($term_id, $taxonomy) {
        if (!function_exists('get_fields')) {
            return array();
        }
        
        $fields = get_fields($taxonomy . '_' . $term_id);
        
        if (empty($fields)) {
            return array();
        }
        
        $formatted_fields = array();
        foreach ($fields as $field_name => $field_value) {
            $field_object = get_field_object($field_name, $taxonomy . '_' . $term_id);
            if ($field_object) {
                $formatted_fields[$field_name] = $this->format_acf_field_value($field_value, $field_object);
            }
        }
        
        return $formatted_fields;
    }
}
