<?php
/**
 * Uninstall script for ACF Custom Post Type Importer
 * 
 * This file is executed when the plugin is uninstalled
 */

// If uninstall not called from WordPress, then exit
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Clean up import files
$upload_dir = wp_upload_dir();
$import_dir = $upload_dir['basedir'] . '/acf-cpt-imports/';

if (is_dir($import_dir)) {
    $files = glob($import_dir . '*');
    foreach ($files as $file) {
        if (is_file($file)) {
            unlink($file);
        }
    }
    rmdir($import_dir);
}

// Clean up options
delete_option('acf_cpt_importer_version');

// Clean up transients
global $wpdb;
$wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_acf_cpt_import_%'");
$wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_acf_cpt_import_%'");
