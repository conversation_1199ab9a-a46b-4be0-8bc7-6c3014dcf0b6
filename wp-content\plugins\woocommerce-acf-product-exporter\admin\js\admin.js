jQuery(document).ready(function($) {
    var exportInProgress = false;
    var progressInterval;
    
    // Handle export form submission
    $('#wc-acf-export-form').on('submit', function(e) {
        e.preventDefault();

        if (exportInProgress) {
            return;
        }

        startExport();
    });

    // Handle test export
    $('#test-export').on('click', function(e) {
        e.preventDefault();

        $.post(wcAcfExporter.ajax_url, {
            action: 'wc_acf_test_export',
            nonce: wcAcfExporter.nonce
        })
        .done(function(response) {
            console.log('Test export response:', response);
            if (response.success) {
                alert('Test export created! Check console for details.');
                showExportComplete(response.data);
            } else {
                alert('Test export failed: ' + (response.data ? response.data.message : 'Unknown error'));
            }
        })
        .fail(function(xhr, status, error) {
            console.error('Test export failed:', xhr, status, error);
            alert('Test export failed: ' + error);
        });
    });
    
    // Start export process
    function startExport() {
        exportInProgress = true;
        
        // Hide form and show progress
        $('.wc-acf-exporter-form').hide();
        $('#export-progress').show();
        $('#export-complete').hide();
        
        // Reset progress
        updateProgress(0, wcAcfExporter.strings.exporting);
        
        // Collect form data
        var formData = {
            action: 'wc_acf_export_products',
            nonce: wcAcfExporter.nonce,
            product_status: $('#product_status').val(),
            product_types: $('#product_types').val(),
            include_variations: $('#include_variations').is(':checked') ? 1 : 0,
            include_acf_fields: $('#include_acf_fields').is(':checked') ? 1 : 0,
            include_acf_taxonomies: $('#include_acf_taxonomies').is(':checked') ? 1 : 0,
            batch_size: $('#batch_size').val()
        };
        
        // Start export
        $.post(wcAcfExporter.ajax_url, formData)
            .done(function(response) {
                console.log('Export response:', response);
                if (response.success) {
                    showExportComplete(response.data);
                } else {
                    var errorMessage = response.data && response.data.message ?
                        response.data.message : wcAcfExporter.strings.export_error;
                    showExportError(errorMessage);
                }
            })
            .fail(function(xhr, status, error) {
                console.error('Export failed:', xhr, status, error);
                showExportError(wcAcfExporter.strings.export_error + ' (' + error + ')');
            })
            .always(function() {
                exportInProgress = false;
                clearInterval(progressInterval);
            });
        
        // Start progress monitoring
        progressInterval = setInterval(checkProgress, 1000);
    }
    
    // Check export progress
    function checkProgress() {
        $.post(wcAcfExporter.ajax_url, {
            action: 'wc_acf_get_export_progress',
            nonce: wcAcfExporter.nonce
        })
        .done(function(response) {
            if (response && response.percentage !== undefined) {
                updateProgress(response.percentage, response.message);
            }
        });
    }
    
    // Update progress display
    function updateProgress(percentage, message) {
        $('#progress-fill').css('width', percentage + '%');
        $('#progress-text').text(message);
        
        // Add to log
        var timestamp = new Date().toLocaleTimeString();
        $('#export-log').append('<div class="log-entry">[' + timestamp + '] ' + message + '</div>');
        
        // Scroll log to bottom
        var logContainer = $('#export-log');
        logContainer.scrollTop(logContainer[0].scrollHeight);
    }
    
    // Show export completion
    function showExportComplete(data) {
        $('#export-progress').hide();
        $('#export-complete').show();

        // Set download links
        $('#download-export').attr('href', data.download_url);

        if (data.instructions_download_url) {
            $('#download-instructions').attr('href', data.instructions_download_url);
        } else {
            $('#download-instructions').hide();
        }

        // Update completion message
        var message = wcAcfExporter.strings.export_complete;
        if (data.total_products) {
            message += ' ' + data.total_products + ' products exported.';
        }
        $('#export-complete p').text(message);
    }
    
    // Show export error
    function showExportError(message) {
        $('#export-progress').hide();
        $('.wc-acf-exporter-form').show();
        
        // Show error message
        if ($('.export-error').length === 0) {
            $('.wc-acf-exporter-container').prepend(
                '<div class="notice notice-error export-error"><p>' + message + '</p></div>'
            );
        } else {
            $('.export-error p').text(message);
        }
        
        // Remove error after 5 seconds
        setTimeout(function() {
            $('.export-error').fadeOut();
        }, 5000);
    }
    
    // Handle download button clicks
    $('#download-export, #download-instructions').on('click', function(e) {
        // Let the browser handle the download
        var isMainExport = $(this).attr('id') === 'download-export';

        if (isMainExport) {
            setTimeout(function() {
                // Reset form after main export download
                resetForm();
            }, 1000);
        }
    });
    
    // Reset form to initial state
    function resetForm() {
        $('.wc-acf-exporter-form').show();
        $('#export-progress').hide();
        $('#export-complete').hide();
        $('.export-error').remove();
        
        // Clear progress
        $('#progress-fill').css('width', '0%');
        $('#progress-text').text('');
        $('#export-log').empty();
        
        exportInProgress = false;
    }
    
    // Add reset button functionality
    $('<button type="button" class="button" id="reset-export">Reset</button>')
        .insertAfter('#start-export')
        .on('click', resetForm);
    
    // Handle page unload warning
    $(window).on('beforeunload', function() {
        if (exportInProgress) {
            return 'Export is in progress. Are you sure you want to leave?';
        }
    });
});
