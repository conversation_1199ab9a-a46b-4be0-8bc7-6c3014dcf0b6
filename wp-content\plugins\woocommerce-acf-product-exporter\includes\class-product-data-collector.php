<?php
/**
 * Product Data Collector class for WooCommerce ACF Product Exporter
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WC_ACF_Product_Data_Collector {
    
    /**
     * Get all products based on criteria
     */
    public function get_products($args = array()) {
        $default_args = array(
            'status' => array('publish'),
            'type' => array('simple', 'variable'),
            'limit' => -1,
            'return' => 'ids'
        );
        
        $args = wp_parse_args($args, $default_args);
        
        return wc_get_products($args);
    }
    
    /**
     * Get complete product data
     */
    public function get_product_data($product_id, $options = array()) {
        $product = wc_get_product($product_id);
        
        if (!$product) {
            return false;
        }
        
        $data = array();
        
        // Basic product data
        $data = array_merge($data, $this->get_basic_product_data($product));
        
        // WooCommerce specific data
        $data = array_merge($data, $this->get_woocommerce_data($product));
        
        // Product meta data
        $data = array_merge($data, $this->get_product_meta($product));
        
        // Taxonomy data
        $data = array_merge($data, $this->get_taxonomy_data($product));
        
        // ACF fields if requested
        if (!empty($options['include_acf_fields'])) {
            $data = array_merge($data, $this->get_acf_fields($product));
        }
        
        // ACF taxonomies if requested
        if (!empty($options['include_acf_taxonomies'])) {
            $data = array_merge($data, $this->get_acf_taxonomies($product));
        }
        
        // Product variations if it's a variable product
        if ($product->is_type('variable') && !empty($options['include_variations'])) {
            return $this->get_variable_product_data($product, $data, $options);
        }
        
        return array($data);
    }
    
    /**
     * Get basic product data
     */
    private function get_basic_product_data($product) {
        return array(
            'ID' => $product->get_id(),
            'post_title' => $product->get_name(),
            'post_content' => $product->get_description(),
            'post_excerpt' => $product->get_short_description(),
            'post_status' => $product->get_status(),
            'post_name' => $product->get_slug(),
            'post_date' => $product->get_date_created() ? $product->get_date_created()->date('Y-m-d H:i:s') : '',
            'post_date_gmt' => $product->get_date_created() ? $product->get_date_created()->date('Y-m-d H:i:s') : '',
            'post_modified' => $product->get_date_modified() ? $product->get_date_modified()->date('Y-m-d H:i:s') : '',
            'post_modified_gmt' => $product->get_date_modified() ? $product->get_date_modified()->date('Y-m-d H:i:s') : '',
            'menu_order' => $product->get_menu_order(),
            'post_type' => 'product',
        );
    }
    
    /**
     * Get WooCommerce specific data
     */
    private function get_woocommerce_data($product) {
        $data = array(
            'product_type' => $product->get_type(),
            'sku' => $product->get_sku(),
            'regular_price' => $product->get_regular_price(),
            'sale_price' => $product->get_sale_price(),
            'price' => $product->get_price(),
            'manage_stock' => $product->get_manage_stock() ? 'yes' : 'no',
            'stock_quantity' => $product->get_stock_quantity(),
            'stock_status' => $product->get_stock_status(),
            'backorders' => $product->get_backorders(),
            'low_stock_amount' => $product->get_low_stock_amount(),
            'sold_individually' => $product->get_sold_individually() ? 'yes' : 'no',
            'weight' => $product->get_weight(),
            'length' => $product->get_length(),
            'width' => $product->get_width(),
            'height' => $product->get_height(),
            'shipping_class' => $product->get_shipping_class(),
            'reviews_allowed' => $product->get_reviews_allowed() ? 'yes' : 'no',
            'purchase_note' => $product->get_purchase_note(),
            'featured' => $product->get_featured() ? 'yes' : 'no',
            'catalog_visibility' => $product->get_catalog_visibility(),
            'virtual' => $product->get_virtual() ? 'yes' : 'no',
            'downloadable' => $product->get_downloadable() ? 'yes' : 'no',
            'tax_status' => $product->get_tax_status(),
            'tax_class' => $product->get_tax_class(),
        );
        
        // Date fields
        if ($product->get_date_on_sale_from()) {
            $data['sale_price_dates_from'] = $product->get_date_on_sale_from()->date('Y-m-d H:i:s');
        }
        
        if ($product->get_date_on_sale_to()) {
            $data['sale_price_dates_to'] = $product->get_date_on_sale_to()->date('Y-m-d H:i:s');
        }
        
        // Images
        $image_id = $product->get_image_id();
        if ($image_id) {
            $data['featured_image'] = wp_get_attachment_url($image_id);
            $data['featured_image_id'] = $image_id;
        }
        
        $gallery_ids = $product->get_gallery_image_ids();
        if (!empty($gallery_ids)) {
            $gallery_urls = array();
            foreach ($gallery_ids as $gallery_id) {
                $gallery_urls[] = wp_get_attachment_url($gallery_id);
            }
            $data['product_gallery'] = implode(',', $gallery_urls);
            $data['product_gallery_ids'] = implode(',', $gallery_ids);
        }
        
        // Downloadable files
        if ($product->is_downloadable()) {
            $downloads = $product->get_downloads();
            if (!empty($downloads)) {
                $download_data = array();
                foreach ($downloads as $download) {
                    $download_data[] = $download->get_name() . '|' . $download->get_file();
                }
                $data['downloadable_files'] = implode(';', $download_data);
            }
            $data['download_limit'] = $product->get_download_limit();
            $data['download_expiry'] = $product->get_download_expiry();
        }
        
        // Upsells and cross-sells
        $upsells = $product->get_upsell_ids();
        if (!empty($upsells)) {
            $data['upsell_ids'] = implode(',', $upsells);
        }
        
        $cross_sells = $product->get_cross_sell_ids();
        if (!empty($cross_sells)) {
            $data['cross_sell_ids'] = implode(',', $cross_sells);
        }
        
        // Grouped products
        if ($product->is_type('grouped')) {
            $children = $product->get_children();
            if (!empty($children)) {
                $data['grouped_products'] = implode(',', $children);
            }
        }
        
        // External product data
        if ($product->is_type('external')) {
            $data['external_url'] = $product->get_product_url();
            $data['button_text'] = $product->get_button_text();
        }
        
        return $data;
    }
    
    /**
     * Get product meta data
     */
    private function get_product_meta($product) {
        $meta_data = array();
        $product_meta = get_post_meta($product->get_id());
        
        // Filter out ACF fields and WooCommerce internal fields
        $excluded_keys = array(
            '_edit_lock', '_edit_last', '_wp_old_slug', '_wp_old_date',
            '_thumbnail_id', '_product_image_gallery', '_sku', '_regular_price',
            '_sale_price', '_price', '_manage_stock', '_stock', '_stock_status',
            '_backorders', '_low_stock_amount', '_sold_individually', '_weight',
            '_length', '_width', '_height', '_shipping_class_id', '_reviews_allowed',
            '_purchase_note', '_featured', '_catalog_visibility', '_virtual',
            '_downloadable', '_tax_status', '_tax_class', '_sale_price_dates_from',
            '_sale_price_dates_to', '_download_limit', '_download_expiry',
            '_upsell_ids', '_crosssell_ids', '_children', '_product_url', '_button_text'
        );
        
        foreach ($product_meta as $key => $value) {
            // Skip ACF fields (they'll be handled separately)
            if (strpos($key, 'field_') === 0) {
                continue;
            }
            
            // Skip excluded WooCommerce fields
            if (in_array($key, $excluded_keys)) {
                continue;
            }
            
            // Skip private fields starting with underscore (except custom ones)
            if (strpos($key, '_') === 0 && !$this->is_custom_meta_field($key)) {
                continue;
            }
            
            $meta_data['meta_' . $key] = is_array($value) ? implode(',', $value) : $value[0];
        }
        
        return $meta_data;
    }
    
    /**
     * Check if meta field is custom (not WooCommerce internal)
     */
    private function is_custom_meta_field($key) {
        $custom_prefixes = array('_custom_', '_user_');
        
        foreach ($custom_prefixes as $prefix) {
            if (strpos($key, $prefix) === 0) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get taxonomy data
     */
    private function get_taxonomy_data($product) {
        $taxonomy_data = array();
        $taxonomies = get_object_taxonomies('product', 'objects');
        
        foreach ($taxonomies as $taxonomy) {
            $terms = wp_get_post_terms($product->get_id(), $taxonomy->name);
            
            if (!empty($terms) && !is_wp_error($terms)) {
                $term_names = array();
                $term_ids = array();
                
                foreach ($terms as $term) {
                    $term_names[] = $term->name;
                    $term_ids[] = $term->term_id;
                }
                
                $taxonomy_data['taxonomy_' . $taxonomy->name] = implode(',', $term_names);
                $taxonomy_data['taxonomy_' . $taxonomy->name . '_ids'] = implode(',', $term_ids);
            }
        }
        
        return $taxonomy_data;
    }
    
    /**
     * Get ACF fields data
     */
    private function get_acf_fields($product) {
        if (!function_exists('get_fields')) {
            return array();
        }
        
        $acf_handler = new WC_ACF_Product_ACF_Handler();
        return $acf_handler->get_product_acf_fields($product->get_id());
    }
    
    /**
     * Get ACF taxonomies data
     */
    private function get_acf_taxonomies($product) {
        if (!function_exists('get_fields')) {
            return array();
        }
        
        $acf_handler = new WC_ACF_Product_ACF_Handler();
        return $acf_handler->get_product_acf_taxonomies($product->get_id());
    }
    
    /**
     * Get variable product data including variations
     */
    private function get_variable_product_data($product, $base_data, $options) {
        $variations_data = array();
        $variations = $product->get_children();
        
        // Add parent product data
        $parent_data = $base_data;
        $parent_data['parent_id'] = '';
        $parent_data['variation_type'] = 'parent';
        $variations_data[] = $parent_data;
        
        // Add variation data
        foreach ($variations as $variation_id) {
            $variation = wc_get_product($variation_id);
            if (!$variation) {
                continue;
            }
            
            $variation_data = $base_data; // Start with parent data
            
            // Override with variation-specific data
            $variation_data['ID'] = $variation->get_id();
            $variation_data['parent_id'] = $product->get_id();
            $variation_data['variation_type'] = 'variation';
            $variation_data['sku'] = $variation->get_sku();
            $variation_data['regular_price'] = $variation->get_regular_price();
            $variation_data['sale_price'] = $variation->get_sale_price();
            $variation_data['price'] = $variation->get_price();
            $variation_data['manage_stock'] = $variation->get_manage_stock() ? 'yes' : 'no';
            $variation_data['stock_quantity'] = $variation->get_stock_quantity();
            $variation_data['stock_status'] = $variation->get_stock_status();
            $variation_data['weight'] = $variation->get_weight();
            $variation_data['length'] = $variation->get_length();
            $variation_data['width'] = $variation->get_width();
            $variation_data['height'] = $variation->get_height();
            
            // Variation attributes
            $attributes = $variation->get_variation_attributes();
            foreach ($attributes as $attribute_name => $attribute_value) {
                $variation_data['attribute_' . $attribute_name] = $attribute_value;
            }
            
            // Variation image
            $variation_image_id = $variation->get_image_id();
            if ($variation_image_id) {
                $variation_data['variation_image'] = wp_get_attachment_url($variation_image_id);
                $variation_data['variation_image_id'] = $variation_image_id;
            }
            
            // ACF fields for variation
            if (!empty($options['include_acf_fields'])) {
                $variation_acf = $this->get_acf_fields($variation);
                $variation_data = array_merge($variation_data, $variation_acf);
            }
            
            $variations_data[] = $variation_data;
        }
        
        return $variations_data;
    }
}
