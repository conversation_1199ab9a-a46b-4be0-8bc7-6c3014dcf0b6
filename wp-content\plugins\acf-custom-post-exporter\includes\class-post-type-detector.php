<?php
/**
 * Post Type Detector Class
 * 
 * Detects and manages available post types for export
 */

if (!defined('ABSPATH')) {
    exit;
}

class ACF_CPT_Post_Type_Detector {
    
    /**
     * Get all available post types that can be exported
     * 
     * @return array
     */
    public function get_available_post_types() {
        $post_types = get_post_types(array('public' => true), 'objects');
        $available_types = array();
        
        // Add built-in post types
        if (isset($post_types['post'])) {
            $available_types['post'] = array(
                'name' => 'post',
                'label' => $post_types['post']->labels->name,
                'count' => wp_count_posts('post')->publish
            );
        }
        
        if (isset($post_types['page'])) {
            $available_types['page'] = array(
                'name' => 'page',
                'label' => $post_types['page']->labels->name,
                'count' => wp_count_posts('page')->publish
            );
        }
        
        // Add WooCommerce product if available
        if (isset($post_types['product'])) {
            $available_types['product'] = array(
                'name' => 'product',
                'label' => $post_types['product']->labels->name,
                'count' => wp_count_posts('product')->publish
            );
        }
        
        // Add custom post types
        $custom_post_types = $this->get_custom_post_types();
        foreach ($custom_post_types as $post_type) {
            if (isset($post_types[$post_type])) {
                $count = wp_count_posts($post_type);
                $total_count = 0;
                
                // Sum all published statuses
                if (is_object($count)) {
                    foreach ($count as $status => $num) {
                        if ($status !== 'trash' && $status !== 'auto-draft') {
                            $total_count += $num;
                        }
                    }
                }
                
                $available_types[$post_type] = array(
                    'name' => $post_type,
                    'label' => $post_types[$post_type]->labels->name,
                    'count' => $total_count
                );
            }
        }
        
        return $available_types;
    }
    
    /**
     * Get custom post types (excluding built-in ones)
     * 
     * @return array
     */
    private function get_custom_post_types() {
        $all_post_types = get_post_types(array('public' => true));
        $built_in_types = array('post', 'page', 'attachment');
        
        return array_diff($all_post_types, $built_in_types);
    }
    
    /**
     * Check if a post type has ACF fields
     * 
     * @param string $post_type
     * @return bool
     */
    public function post_type_has_acf_fields($post_type) {
        if (!function_exists('acf_get_field_groups')) {
            return false;
        }
        
        $field_groups = acf_get_field_groups(array(
            'post_type' => $post_type
        ));
        
        return !empty($field_groups);
    }
    
    /**
     * Get post type object
     * 
     * @param string $post_type
     * @return object|null
     */
    public function get_post_type_object($post_type) {
        return get_post_type_object($post_type);
    }
    
    /**
     * Validate if post type exists and is public
     * 
     * @param string $post_type
     * @return bool
     */
    public function is_valid_post_type($post_type) {
        $post_type_obj = get_post_type_object($post_type);
        return $post_type_obj && $post_type_obj->public;
    }
    
    /**
     * Get posts count for a specific post type
     * 
     * @param string $post_type
     * @return int
     */
    public function get_posts_count($post_type) {
        $count = wp_count_posts($post_type);
        $total = 0;
        
        if (is_object($count)) {
            foreach ($count as $status => $num) {
                if ($status !== 'trash' && $status !== 'auto-draft') {
                    $total += $num;
                }
            }
        }
        
        return $total;
    }
    
    /**
     * Get sample posts for preview
     * 
     * @param string $post_type
     * @param int $limit
     * @return array
     */
    public function get_sample_posts($post_type, $limit = 5) {
        $args = array(
            'post_type' => $post_type,
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'orderby' => 'date',
            'order' => 'DESC'
        );
        
        $posts = get_posts($args);
        $sample_data = array();
        
        foreach ($posts as $post) {
            $sample_data[] = array(
                'ID' => $post->ID,
                'title' => $post->post_title,
                'date' => $post->post_date,
                'status' => $post->post_status
            );
        }
        
        return $sample_data;
    }
}
