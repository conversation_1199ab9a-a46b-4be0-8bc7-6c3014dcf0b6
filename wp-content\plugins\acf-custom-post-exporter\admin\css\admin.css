/* ACF Custom Post Type Exporter Admin Styles */

.acf-cpt-exporter-container {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.acf-cpt-exporter-main {
    flex: 1;
    max-width: 800px;
}

.acf-cpt-exporter-sidebar {
    width: 300px;
    flex-shrink: 0;
}

/* Steps */
.acf-cpt-step {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.acf-cpt-step h2 {
    margin-top: 0;
    color: #23282d;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
}

/* Post Type Selection */
.post-type-selection {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 15px;
}

.post-type-info {
    background: #f0f6fc;
    border: 1px solid #c3dbf0;
    border-radius: 4px;
    padding: 10px;
    margin-top: 15px;
}

/* Fields Selection */
.fields-selection {
    margin-bottom: 20px;
}

.field-groups {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.field-group {
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 15px;
    background: #fafafa;
}

.field-group h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #23282d;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.field-group h4 {
    margin: 15px 0 10px 0;
    color: #0073aa;
    font-size: 13px;
    font-weight: 600;
}

.fields-list {
    max-height: 300px;
    overflow-y: auto;
}

.field-checkbox {
    display: block;
    margin-bottom: 8px;
    padding: 5px;
    cursor: pointer;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.field-checkbox:hover {
    background-color: #f0f0f0;
}

.field-checkbox input[type="checkbox"] {
    margin-right: 8px;
}

.field-type {
    color: #666;
    font-size: 12px;
    font-style: italic;
}

.field-actions {
    display: flex;
    gap: 10px;
}

/* Export Options */
.export-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

/* Progress */
.progress-container {
    text-align: center;
    padding: 20px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005a87);
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    color: #666;
    font-style: italic;
}

/* Sidebar */
.sidebar-box {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.sidebar-box h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #23282d;
    font-size: 14px;
}

.sidebar-box h4 {
    margin: 15px 0 10px 0;
    color: #0073aa;
    font-size: 13px;
}

.sidebar-box ul {
    margin: 0;
    padding-left: 20px;
}

.sidebar-box li {
    margin-bottom: 5px;
    color: #666;
    font-size: 13px;
}

/* Responsive */
@media (max-width: 1200px) {
    .acf-cpt-exporter-container {
        flex-direction: column;
    }
    
    .acf-cpt-exporter-sidebar {
        width: 100%;
    }
    
    .field-groups {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .post-type-selection {
        flex-direction: column;
        align-items: stretch;
    }
    
    .export-actions {
        flex-direction: column;
    }
    
    .field-actions {
        flex-direction: column;
    }
}

/* Notices */
.notice {
    padding: 12px;
    margin: 15px 0;
    border-left: 4px solid;
    background: #fff;
}

.notice-success {
    border-left-color: #46b450;
}

.notice-error {
    border-left-color: #dc3232;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Button enhancements */
.button-large {
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
}

/* Form table adjustments */
.form-table th {
    width: 200px;
}

.form-table td .description {
    margin-top: 5px;
    color: #666;
    font-style: italic;
}

/* Field group fields container */
.field-group-fields {
    margin-left: 15px;
    border-left: 2px solid #e1e1e1;
    padding-left: 15px;
}
