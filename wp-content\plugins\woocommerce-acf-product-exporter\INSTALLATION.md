# Installation Guide - WooCommerce ACF Product Exporter

## Quick Installation

1. **Upload the Plugin**
   - Upload the entire `woocommerce-acf-product-exporter` folder to `/wp-content/plugins/`
   - Or zip the folder and upload via WordPress admin (Plugins > Add New > Upload Plugin)

2. **Activate the Plugin**
   - Go to WordPress Admin > Plugins
   - Find "WooCommerce ACF Product Exporter" in the list
   - Click "Activate"

3. **Access the Exporter**
   - Navigate to WooCommerce > ACF Product Exporter
   - Configure your export settings
   - Start your first export!

## Requirements Check

Before installation, ensure your site meets these requirements:

### Required Plugins
- ✅ **WordPress 5.0+**
- ✅ **WooCommerce 5.0+** (must be active)
- ✅ **Advanced Custom Fields Pro** (for ACF functionality)

### Server Requirements
- ✅ **PHP 7.4+**
- ✅ **Memory Limit**: 256MB+ recommended for large catalogs
- ✅ **Execution Time**: 300+ seconds for large exports
- ✅ **File Permissions**: Write access to `/wp-content/uploads/`

## Verification Steps

After installation, verify everything is working:

### 1. Check Plugin Activation
- Go to Plugins page
- Confirm "WooCommerce ACF Product Exporter" shows as active
- No error messages should appear

### 2. Check Menu Access
- Go to WooCommerce menu
- Look for "ACF Product Exporter" submenu item
- Click to access the export page

### 3. Check Dependencies
The plugin will show warnings if required plugins are missing:
- **WooCommerce**: "WooCommerce ACF Product Exporter requires WooCommerce to be installed and active."
- **ACF Pro**: ACF features will be disabled if not available

### 4. Test Export Directory
The plugin creates `/wp-content/uploads/wc-acf-exports/` automatically.
If you see permission errors, ensure WordPress can write to the uploads directory.

## Troubleshooting Installation

### Common Issues

**Plugin Not Appearing in Menu**
- Check if WooCommerce is active
- Verify user has `manage_woocommerce` capability
- Check for PHP errors in error logs

**Memory Errors During Activation**
- Increase PHP memory limit in wp-config.php:
  ```php
  ini_set('memory_limit', '512M');
  ```

**File Permission Errors**
- Ensure uploads directory is writable:
  ```bash
  chmod 755 /wp-content/uploads/
  ```

**Missing ACF Features**
- Install Advanced Custom Fields Pro
- Activate the ACF Pro plugin
- Refresh the exporter page

### Debug Mode

Enable WordPress debug mode for detailed error information:

```php
// Add to wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

Check `/wp-content/debug.log` for any error messages.

## Manual Installation

If automatic installation fails:

1. **Download/Create Plugin Files**
   - Ensure all plugin files are in the correct structure
   - Main file: `woocommerce-acf-product-exporter.php`
   - Admin files: `admin/` directory
   - Core files: `includes/` directory

2. **Set Correct Permissions**
   ```bash
   # Plugin directory
   chmod 755 woocommerce-acf-product-exporter/
   
   # PHP files
   chmod 644 *.php
   
   # Directories
   chmod 755 admin/ includes/ templates/
   ```

3. **Verify File Structure**
   ```
   woocommerce-acf-product-exporter/
   ├── woocommerce-acf-product-exporter.php
   ├── README.md
   ├── INSTALLATION.md
   ├── admin/
   │   ├── class-admin.php
   │   ├── css/admin.css
   │   └── js/admin.js
   ├── includes/
   │   ├── class-product-data-collector.php
   │   ├── class-csv-exporter.php
   │   └── class-acf-handler.php
   └── templates/
       └── import-mapping-guide.php
   ```

## Post-Installation Setup

### 1. Configure Server Settings (Optional)

For large product catalogs, consider adjusting:

```php
// In wp-config.php or .htaccess
ini_set('max_execution_time', 600);
ini_set('memory_limit', '512M');
ini_set('max_input_vars', 3000);
```

### 2. Test with Sample Products

1. Create a few test products with ACF fields
2. Run a small export to verify functionality
3. Check the generated CSV file
4. Test the import instructions

### 3. Backup Considerations

Before running large exports:
- Backup your database
- Monitor server resources
- Test with small batches first

## Uninstallation

To remove the plugin:

1. **Deactivate Plugin**
   - Go to Plugins page
   - Click "Deactivate" for the plugin

2. **Delete Plugin Files**
   - Click "Delete" to remove all plugin files
   - Or manually delete the plugin directory

3. **Clean Up (Optional)**
   - Remove export files from `/wp-content/uploads/wc-acf-exports/`
   - Clean up any custom database options if needed

## Support

If you encounter issues:

1. Check this installation guide
2. Review the main README.md file
3. Enable debug mode and check error logs
4. Verify all requirements are met
5. Test with a minimal plugin setup

## Next Steps

After successful installation:
- Read the main README.md for usage instructions
- Configure your first export
- Review the import mapping guide
- Test with sample data before production use
