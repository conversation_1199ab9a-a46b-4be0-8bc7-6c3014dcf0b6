<?php
/**
 * Plugin Name: ACF Custom Post Type Exporter
 * Plugin URI: https://yourwebsite.com
 * Description: Export ACF field values from any custom post type to CSV format. Supports all post types including WooCommerce products, custom post types, and standard WordPress posts.
 * Version: 1.0.0
 * Author: Hisense Development Team
 * Author URI: https://hisense.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: acf-custom-post-exporter
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('ACF_CPT_EXPORTER_VERSION', '1.0.0');
define('ACF_CPT_EXPORTER_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('ACF_CPT_EXPORTER_PLUGIN_URL', plugin_dir_url(__FILE__));
define('ACF_CPT_EXPORTER_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main plugin class
 */
class ACF_Custom_Post_Exporter {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if ACF is active
        if (!function_exists('get_field')) {
            add_action('admin_notices', array($this, 'acf_missing_notice'));
            return;
        }
        
        // Load text domain
        load_plugin_textdomain('acf-custom-post-exporter', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Include required files
        $this->includes();
        
        // Initialize admin
        if (is_admin()) {
            $this->init_admin();
        }
        
        // Initialize AJAX handlers
        $this->init_ajax();
    }
    
    /**
     * Include required files
     */
    private function includes() {
        require_once ACF_CPT_EXPORTER_PLUGIN_DIR . 'includes/class-post-type-detector.php';
        require_once ACF_CPT_EXPORTER_PLUGIN_DIR . 'includes/class-acf-field-detector.php';
        require_once ACF_CPT_EXPORTER_PLUGIN_DIR . 'includes/class-data-exporter.php';
        require_once ACF_CPT_EXPORTER_PLUGIN_DIR . 'includes/class-csv-generator.php';
        
        if (is_admin()) {
            require_once ACF_CPT_EXPORTER_PLUGIN_DIR . 'admin/class-admin-page.php';
        }
    }
    
    /**
     * Initialize admin
     */
    private function init_admin() {
        new ACF_CPT_Exporter_Admin_Page();
    }
    
    /**
     * Initialize AJAX handlers
     */
    private function init_ajax() {
        add_action('wp_ajax_acf_cpt_get_post_types', array($this, 'ajax_get_post_types'));
        add_action('wp_ajax_acf_cpt_get_acf_fields', array($this, 'ajax_get_acf_fields'));
        add_action('wp_ajax_acf_cpt_export_data', array($this, 'ajax_export_data'));
        add_action('wp_ajax_acf_cpt_download_file', array($this, 'ajax_download_file'));
    }
    
    /**
     * AJAX handler to get available post types
     */
    public function ajax_get_post_types() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'acf_cpt_export_nonce')) {
            wp_send_json_error(__('Security check failed', 'acf-custom-post-exporter'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'acf-custom-post-exporter'));
        }

        try {
            $detector = new ACF_CPT_Post_Type_Detector();
            $post_types = $detector->get_available_post_types();
            wp_send_json_success($post_types);
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Failed to get post types: ', 'acf-custom-post-exporter') . $e->getMessage()
            ));
        }
    }
    
    /**
     * AJAX handler to get ACF fields for a post type
     */
    public function ajax_get_acf_fields() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'acf_cpt_export_nonce')) {
            wp_send_json_error(__('Security check failed', 'acf-custom-post-exporter'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'acf-custom-post-exporter'));
        }

        $post_type = sanitize_text_field($_POST['post_type']);

        try {
            $detector = new ACF_CPT_Field_Detector();
            $all_fields = $detector->get_all_fields_for_post_type($post_type);
            wp_send_json_success($all_fields);
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Failed to get ACF fields: ', 'acf-custom-post-exporter') . $e->getMessage()
            ));
        }
    }
    
    /**
     * AJAX handler for data export
     */
    public function ajax_export_data() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'acf_cpt_export_nonce')) {
            wp_send_json_error(__('Security check failed', 'acf-custom-post-exporter'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'acf-custom-post-exporter'));
        }

        try {
            $exporter = new ACF_CPT_Data_Exporter();
            $result = $exporter->export($_POST);

            if ($result['success']) {
                wp_send_json_success($result);
            } else {
                wp_send_json_error($result);
            }
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Export failed: ', 'acf-custom-post-exporter') . $e->getMessage()
            ));
        }
    }
    
    /**
     * AJAX handler for file download
     */
    public function ajax_download_file() {
        // Verify nonce
        if (!wp_verify_nonce($_GET['nonce'], 'acf_cpt_download_nonce')) {
            wp_die(__('Security check failed', 'acf-custom-post-exporter'));
        }

        $filename = sanitize_file_name($_GET['file']);
        $upload_dir = wp_upload_dir();
        $export_dir = $upload_dir['basedir'] . '/acf-cpt-exports/';
        $file_path = $export_dir . $filename;

        if (!file_exists($file_path)) {
            wp_die(__('File not found', 'acf-custom-post-exporter'));
        }

        // Set headers for download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($file_path));
        
        // Output file
        readfile($file_path);
        
        // Clean up file after download
        unlink($file_path);
        exit;
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create export directory
        $upload_dir = wp_upload_dir();
        $export_dir = $upload_dir['basedir'] . '/acf-cpt-exports/';
        
        if (!file_exists($export_dir)) {
            wp_mkdir_p($export_dir);
        }
        
        update_option('acf_cpt_exporter_version', ACF_CPT_EXPORTER_VERSION);
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up temporary files
        $this->cleanup_temp_files();
    }
    
    /**
     * Clean up temporary files
     */
    private function cleanup_temp_files() {
        $upload_dir = wp_upload_dir();
        $export_dir = $upload_dir['basedir'] . '/acf-cpt-exports/';
        
        if (is_dir($export_dir)) {
            $files = glob($export_dir . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
    }
    
    /**
     * ACF missing notice
     */
    public function acf_missing_notice() {
        echo '<div class="notice notice-error"><p>';
        echo __('ACF Custom Post Type Exporter requires Advanced Custom Fields (ACF) to be installed and active.', 'acf-custom-post-exporter');
        echo '</p></div>';
    }
}

// Initialize the plugin
ACF_Custom_Post_Exporter::get_instance();
