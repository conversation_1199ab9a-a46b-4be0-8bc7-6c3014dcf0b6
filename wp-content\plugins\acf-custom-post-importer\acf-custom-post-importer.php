<?php
/**
 * Plugin Name: ACF Custom Post Type Importer
 * Plugin URI: https://yourwebsite.com
 * Description: Import ACF field values to any custom post type from CSV format. Companion plugin to ACF Custom Post Type Exporter.
 * Version: 1.0.0
 * Author: Hisense Development Team
 * Author URI: https://hisense.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: acf-custom-post-importer
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('ACF_CPT_IMPORTER_VERSION', '1.0.0');
define('ACF_CPT_IMPORTER_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('ACF_CPT_IMPORTER_PLUGIN_URL', plugin_dir_url(__FILE__));
define('ACF_CPT_IMPORTER_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main plugin class
 */
class ACF_Custom_Post_Importer {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if ACF is active
        if (!function_exists('get_field')) {
            add_action('admin_notices', array($this, 'acf_missing_notice'));
            return;
        }
        
        // Load text domain
        load_plugin_textdomain('acf-custom-post-importer', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Include required files
        $this->includes();
        
        // Initialize admin
        if (is_admin()) {
            $this->init_admin();
        }
        
        // Initialize AJAX handlers
        $this->init_ajax();
    }
    
    /**
     * Include required files
     */
    private function includes() {
        require_once ACF_CPT_IMPORTER_PLUGIN_DIR . 'includes/class-csv-parser.php';
        require_once ACF_CPT_IMPORTER_PLUGIN_DIR . 'includes/class-field-mapper.php';
        require_once ACF_CPT_IMPORTER_PLUGIN_DIR . 'includes/class-data-importer.php';
        require_once ACF_CPT_IMPORTER_PLUGIN_DIR . 'includes/class-import-validator.php';
        
        if (is_admin()) {
            require_once ACF_CPT_IMPORTER_PLUGIN_DIR . 'admin/class-admin-page.php';
        }
    }
    
    /**
     * Initialize admin
     */
    private function init_admin() {
        new ACF_CPT_Importer_Admin_Page();
    }
    
    /**
     * Initialize AJAX handlers
     */
    private function init_ajax() {
        add_action('wp_ajax_acf_cpt_upload_csv', array($this, 'ajax_upload_csv'));
        add_action('wp_ajax_acf_cpt_parse_csv', array($this, 'ajax_parse_csv'));
        add_action('wp_ajax_acf_cpt_import_data', array($this, 'ajax_import_data'));
        add_action('wp_ajax_acf_cpt_get_import_progress', array($this, 'ajax_get_import_progress'));
        add_action('wp_ajax_acf_cpt_get_post_types', array($this, 'ajax_get_post_types'));
        add_action('wp_ajax_acf_cpt_get_acf_fields', array($this, 'ajax_get_acf_fields'));
    }
    
    /**
     * AJAX handler for CSV upload
     */
    public function ajax_upload_csv() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'acf_cpt_import_nonce')) {
            wp_send_json_error(__('Security check failed', 'acf-custom-post-importer'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'acf-custom-post-importer'));
        }

        try {
            $parser = new ACF_CPT_CSV_Parser();
            $result = $parser->handle_upload();
            
            if ($result['success']) {
                wp_send_json_success($result);
            } else {
                wp_send_json_error($result);
            }
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Upload failed: ', 'acf-custom-post-importer') . $e->getMessage()
            ));
        }
    }
    
    /**
     * AJAX handler for CSV parsing
     */
    public function ajax_parse_csv() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'acf_cpt_import_nonce')) {
            wp_send_json_error(__('Security check failed', 'acf-custom-post-importer'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'acf-custom-post-importer'));
        }

        $file_path = sanitize_text_field($_POST['file_path']);
        
        try {
            $parser = new ACF_CPT_CSV_Parser();
            $result = $parser->parse_csv($file_path);
            wp_send_json_success($result);
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Failed to parse CSV: ', 'acf-custom-post-importer') . $e->getMessage()
            ));
        }
    }
    
    /**
     * AJAX handler for data import
     */
    public function ajax_import_data() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'acf_cpt_import_nonce')) {
            wp_send_json_error(__('Security check failed', 'acf-custom-post-importer'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'acf-custom-post-importer'));
        }

        try {
            $importer = new ACF_CPT_Data_Importer();
            $result = $importer->import($_POST);

            if ($result['success']) {
                wp_send_json_success($result);
            } else {
                wp_send_json_error($result);
            }
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Import failed: ', 'acf-custom-post-importer') . $e->getMessage()
            ));
        }
    }
    
    /**
     * AJAX handler for import progress
     */
    public function ajax_get_import_progress() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'acf_cpt_import_nonce')) {
            wp_die(__('Security check failed', 'acf-custom-post-importer'));
        }

        $progress = get_transient('acf_cpt_import_progress_' . get_current_user_id());
        wp_send_json($progress ? $progress : array('status' => 'not_found'));
    }

    /**
     * AJAX handler to get available post types
     */
    public function ajax_get_post_types() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'acf_cpt_import_nonce')) {
            wp_send_json_error(__('Security check failed', 'acf-custom-post-importer'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'acf-custom-post-importer'));
        }

        try {
            // Reuse the detector from the exporter plugin if available
            if (class_exists('ACF_CPT_Post_Type_Detector')) {
                $detector = new ACF_CPT_Post_Type_Detector();
                $post_types = $detector->get_available_post_types();
            } else {
                // Fallback implementation
                $post_types = $this->get_available_post_types_fallback();
            }
            wp_send_json_success($post_types);
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Failed to get post types: ', 'acf-custom-post-importer') . $e->getMessage()
            ));
        }
    }

    /**
     * AJAX handler to get ACF fields for a post type
     */
    public function ajax_get_acf_fields() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'acf_cpt_import_nonce')) {
            wp_send_json_error(__('Security check failed', 'acf-custom-post-importer'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'acf-custom-post-importer'));
        }

        $post_type = sanitize_text_field($_POST['post_type']);

        try {
            $mapper = new ACF_CPT_Field_Mapper();
            $fields = $mapper->get_available_fields($post_type);
            wp_send_json_success($fields);
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Failed to get ACF fields: ', 'acf-custom-post-importer') . $e->getMessage()
            ));
        }
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create import directory
        $upload_dir = wp_upload_dir();
        $import_dir = $upload_dir['basedir'] . '/acf-cpt-imports/';
        
        if (!file_exists($import_dir)) {
            wp_mkdir_p($import_dir);
        }
        
        update_option('acf_cpt_importer_version', ACF_CPT_IMPORTER_VERSION);
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up temporary files
        $this->cleanup_temp_files();
    }
    
    /**
     * Clean up temporary files
     */
    private function cleanup_temp_files() {
        $upload_dir = wp_upload_dir();
        $import_dir = $upload_dir['basedir'] . '/acf-cpt-imports/';
        
        if (is_dir($import_dir)) {
            $files = glob($import_dir . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
    }
    
    /**
     * Fallback method to get available post types
     */
    private function get_available_post_types_fallback() {
        $post_types = get_post_types(array('public' => true), 'objects');
        $available_types = array();

        foreach ($post_types as $post_type) {
            $count = wp_count_posts($post_type->name);
            $total_count = 0;

            if (is_object($count)) {
                foreach ($count as $status => $num) {
                    if ($status !== 'trash' && $status !== 'auto-draft') {
                        $total_count += $num;
                    }
                }
            }

            $available_types[$post_type->name] = array(
                'name' => $post_type->name,
                'label' => $post_type->labels->name,
                'count' => $total_count
            );
        }

        return $available_types;
    }

    /**
     * ACF missing notice
     */
    public function acf_missing_notice() {
        echo '<div class="notice notice-error"><p>';
        echo __('ACF Custom Post Type Importer requires Advanced Custom Fields (ACF) to be installed and active.', 'acf-custom-post-importer');
        echo '</p></div>';
    }
}

// Initialize the plugin
ACF_Custom_Post_Importer::get_instance();
