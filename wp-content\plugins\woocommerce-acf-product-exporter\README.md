# WooCommerce ACF Product Exporter

A comprehensive WordPress plugin that exports WooCommerce products including ACF (Advanced Custom Fields) fields, ACF taxonomies, and all default WooCommerce values in CSV format for easy import to another WooCommerce site.

## Features

- **Complete Product Export**: Exports all WooCommerce product data including variations, meta fields, and taxonomies
- **ACF Fields Support**: Exports all ACF custom fields associated with products
- **ACF Taxonomies Support**: Exports ACF custom taxonomies and their terms
- **Variable Products**: Supports variable products with all variations exported as separate rows
- **Batch Processing**: Processes products in batches to handle large catalogs efficiently
- **Progress Tracking**: Real-time progress tracking with detailed logging
- **Import Ready**: CSV format optimized for importing into another WooCommerce site
- **User-Friendly Interface**: Clean admin interface with export options and settings

## Requirements

- WordPress 5.0 or higher
- WooCommerce 5.0 or higher
- Advanced Custom Fields Pro (for ACF functionality)
- PHP 7.4 or higher

## Installation

1. Upload the plugin folder to `/wp-content/plugins/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Navigate to WooCommerce > ACF Product Exporter

## Usage

### Basic Export

1. Go to **WooCommerce > ACF Product Exporter**
2. Configure your export options:
   - **Product Status**: Select which product statuses to export
   - **Product Types**: Choose product types (simple, variable, grouped, external)
   - **Include Variations**: Export product variations as separate rows
   - **Include ACF Fields**: Export all ACF custom fields
   - **Include ACF Taxonomies**: Export ACF custom taxonomies
   - **Batch Size**: Number of products to process at once
3. Click **Start Export**
4. Monitor the progress in real-time
5. Download the CSV file when complete

### Export Options

#### Product Status
- Published
- Draft
- Private
- Pending

#### Product Types
- Simple Products
- Variable Products (with variations)
- Grouped Products
- External/Affiliate Products

#### Advanced Options
- **Include Variations**: When enabled, variable products will have each variation exported as a separate row
- **Include ACF Fields**: Exports all ACF custom fields with proper formatting
- **Include ACF Taxonomies**: Exports custom taxonomies created with ACF
- **Batch Size**: Adjust based on server memory (10-500 products per batch)

## Exported Data

### WooCommerce Core Data
- Product ID, Title, Description, Short Description
- SKU, Prices (Regular, Sale, Current)
- Stock Management (Quantity, Status, Backorders)
- Dimensions (Weight, Length, Width, Height)
- Images (Featured Image, Gallery)
- Categories, Tags, Shipping Classes
- Product Type, Status, Visibility
- Downloadable Files (for digital products)
- Upsells, Cross-sells, Grouped Products

### ACF Fields
- All custom fields created with Advanced Custom Fields
- Proper formatting for different field types:
  - Images: URLs
  - Galleries: Comma-separated URLs
  - Post Objects: Post IDs
  - Taxonomies: Term names
  - Repeater Fields: Structured format
  - Group Fields: Nested data

### ACF Taxonomies
- Custom taxonomies created with ACF
- Term names and IDs
- ACF fields attached to taxonomy terms

### Product Variations
- All variation-specific data
- Variation attributes
- Variation images
- Variation-specific ACF fields

## CSV Format

The exported CSV includes:
- Headers with field names
- Properly escaped values
- UTF-8 encoding
- Import-ready format for WooCommerce

### Sample CSV Structure
```csv
ID,post_title,sku,regular_price,acf_custom_field,taxonomy_product_cat,...
123,"Product Name","SKU123",29.99,"Custom Value","Category Name",...
```

## Import Compatibility

The exported CSV is designed to be compatible with:
- WooCommerce native import functionality
- Popular import plugins
- Custom import scripts

### Import Tips
1. Use the WooCommerce Product CSV Importer
2. Map ACF fields during import
3. Ensure ACF field groups exist on target site
4. Import custom taxonomies before products

## Troubleshooting

### Common Issues

**Memory Errors**
- Reduce batch size in export options
- Increase PHP memory limit
- Export in smaller chunks

**Timeout Issues**
- Reduce batch size
- Increase PHP max execution time
- Use server with better resources

**Missing ACF Data**
- Ensure ACF Pro is installed and active
- Check field group locations
- Verify field permissions

**Large File Downloads**
- Files are temporarily stored in `/wp-content/uploads/wc-acf-exports/`
- Files are automatically cleaned up after download
- Check server disk space

### Debug Mode
Enable WordPress debug mode to see detailed error messages:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Hooks and Filters

### Filters
```php
// Modify export data before CSV generation
add_filter('wc_acf_export_product_data', 'custom_modify_export_data', 10, 2);

// Modify CSV headers
add_filter('wc_acf_export_csv_headers', 'custom_modify_headers', 10, 1);

// Modify batch size
add_filter('wc_acf_export_batch_size', 'custom_batch_size', 10, 1);
```

### Actions
```php
// Before export starts
add_action('wc_acf_export_before_start', 'custom_before_export');

// After export completes
add_action('wc_acf_export_after_complete', 'custom_after_export', 10, 2);
```

## Performance Considerations

- **Large Catalogs**: Use smaller batch sizes (10-25 products)
- **Memory Usage**: Monitor server memory during export
- **Server Resources**: Consider running exports during off-peak hours
- **File Size**: Large exports may take time to download

## Security

- Requires `manage_woocommerce` capability
- Nonce verification for all AJAX requests
- Temporary files are user-specific
- Files are automatically cleaned up

## Support

For support and feature requests:
1. Check the troubleshooting section
2. Enable debug mode for error details
3. Contact your developer for custom modifications

## Changelog

### Version 1.0.0
- Initial release
- Complete WooCommerce product export
- ACF fields and taxonomies support
- Variable products with variations
- Batch processing with progress tracking
- Import-ready CSV format

## License

This plugin is licensed under the GPL v2 or later.

## Credits

Developed for comprehensive WooCommerce product migration with full ACF support.
