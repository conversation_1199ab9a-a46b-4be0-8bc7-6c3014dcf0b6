<?php
/**
 * Admin class for WooCommerce ACF Product Exporter
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class WC_ACF_Product_Exporter_Admin {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('init', array($this, 'handle_export_download'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('ACF Product Exporter', 'wc-acf-product-exporter'),
            __('ACF Product Exporter', 'wc-acf-product-exporter'),
            'manage_woocommerce',
            'wc-acf-product-exporter',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        if ('woocommerce_page_wc-acf-product-exporter' !== $hook) {
            return;
        }
        
        wp_enqueue_script(
            'wc-acf-product-exporter-admin',
            WC_ACF_PRODUCT_EXPORTER_PLUGIN_URL . 'admin/js/admin.js',
            array('jquery'),
            WC_ACF_PRODUCT_EXPORTER_VERSION,
            true
        );
        
        wp_enqueue_style(
            'wc-acf-product-exporter-admin',
            WC_ACF_PRODUCT_EXPORTER_PLUGIN_URL . 'admin/css/admin.css',
            array(),
            WC_ACF_PRODUCT_EXPORTER_VERSION
        );
        
        wp_localize_script('wc-acf-product-exporter-admin', 'wcAcfExporter', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wc_acf_export_nonce'),
            'strings' => array(
                'exporting' => __('Exporting products...', 'wc-acf-product-exporter'),
                'export_complete' => __('Export completed successfully!', 'wc-acf-product-exporter'),
                'export_error' => __('Export failed. Please try again.', 'wc-acf-product-exporter'),
                'download_ready' => __('Download ready!', 'wc-acf-product-exporter'),
            )
        ));
    }
    
    /**
     * Handle export file download
     */
    public function handle_export_download() {
        if (!isset($_GET['action']) || $_GET['action'] !== 'download_export') {
            return;
        }
        
        if (!isset($_GET['file']) || !isset($_GET['nonce'])) {
            return;
        }
        
        if (!wp_verify_nonce($_GET['nonce'], 'wc_acf_download_nonce')) {
            wp_die(__('Security check failed', 'wc-acf-product-exporter'));
        }
        
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Insufficient permissions', 'wc-acf-product-exporter'));
        }
        
        $filename = sanitize_file_name($_GET['file']);
        $upload_dir = wp_upload_dir();
        $file_path = $upload_dir['basedir'] . '/wc-acf-exports/' . $filename;
        
        if (!file_exists($file_path)) {
            wp_die(__('File not found', 'wc-acf-product-exporter'));
        }
        
        // Determine content type
        $content_type = 'text/csv';
        if (pathinfo($filename, PATHINFO_EXTENSION) === 'txt') {
            $content_type = 'text/plain';
        }

        // Clear any previous output
        if (ob_get_level()) {
            ob_end_clean();
        }

        // Set headers for download
        header('Content-Type: ' . $content_type);
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($file_path));
        header('Cache-Control: must-revalidate');
        header('Pragma: public');

        // Output file
        readfile($file_path);

        // Clean up file after download
        unlink($file_path);
        exit;
    }
    
    /**
     * Admin page content
     */
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

            <?php
            // Show debug information
            $upload_dir = wp_upload_dir();
            $export_dir = $upload_dir['basedir'] . '/wc-acf-exports/';
            ?>
            <div class="notice notice-info">
                <p><strong>Debug Info:</strong></p>
                <ul>
                    <li>Upload Dir: <?php echo esc_html($upload_dir['basedir']); ?></li>
                    <li>Export Dir: <?php echo esc_html($export_dir); ?></li>
                    <li>Export Dir Exists: <?php echo file_exists($export_dir) ? 'Yes' : 'No'; ?></li>
                    <li>Export Dir Writable: <?php echo is_writable(dirname($export_dir)) ? 'Yes' : 'No'; ?></li>
                    <li>WooCommerce Active: <?php echo class_exists('WooCommerce') ? 'Yes' : 'No'; ?></li>
                    <li>ACF Active: <?php echo function_exists('get_fields') ? 'Yes' : 'No'; ?></li>
                </ul>
            </div>

            <div class="wc-acf-exporter-container">
                <div class="wc-acf-exporter-form">
                    <h2><?php _e('Export Options', 'wc-acf-product-exporter'); ?></h2>
                    
                    <form id="wc-acf-export-form">
                        <?php wp_nonce_field('wc_acf_export_nonce', 'nonce'); ?>
                        
                        <table class="form-table">
                            <tr>
                                <th scope="row">
                                    <label for="product_status"><?php _e('Product Status', 'wc-acf-product-exporter'); ?></label>
                                </th>
                                <td>
                                    <select name="product_status" id="product_status" multiple>
                                        <option value="publish" selected><?php _e('Published', 'wc-acf-product-exporter'); ?></option>
                                        <option value="draft"><?php _e('Draft', 'wc-acf-product-exporter'); ?></option>
                                        <option value="private"><?php _e('Private', 'wc-acf-product-exporter'); ?></option>
                                        <option value="pending"><?php _e('Pending', 'wc-acf-product-exporter'); ?></option>
                                    </select>
                                    <p class="description"><?php _e('Select which product statuses to export. Hold Ctrl/Cmd to select multiple.', 'wc-acf-product-exporter'); ?></p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="product_types"><?php _e('Product Types', 'wc-acf-product-exporter'); ?></label>
                                </th>
                                <td>
                                    <select name="product_types" id="product_types" multiple>
                                        <option value="simple" selected><?php _e('Simple', 'wc-acf-product-exporter'); ?></option>
                                        <option value="variable" selected><?php _e('Variable', 'wc-acf-product-exporter'); ?></option>
                                        <option value="grouped"><?php _e('Grouped', 'wc-acf-product-exporter'); ?></option>
                                        <option value="external"><?php _e('External/Affiliate', 'wc-acf-product-exporter'); ?></option>
                                    </select>
                                    <p class="description"><?php _e('Select which product types to export.', 'wc-acf-product-exporter'); ?></p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="include_variations"><?php _e('Include Variations', 'wc-acf-product-exporter'); ?></label>
                                </th>
                                <td>
                                    <input type="checkbox" name="include_variations" id="include_variations" value="1" checked>
                                    <label for="include_variations"><?php _e('Export product variations as separate rows', 'wc-acf-product-exporter'); ?></label>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="include_acf_fields"><?php _e('Include ACF Fields', 'wc-acf-product-exporter'); ?></label>
                                </th>
                                <td>
                                    <input type="checkbox" name="include_acf_fields" id="include_acf_fields" value="1" checked>
                                    <label for="include_acf_fields"><?php _e('Export all ACF custom fields', 'wc-acf-product-exporter'); ?></label>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="include_acf_taxonomies"><?php _e('Include ACF Taxonomies', 'wc-acf-product-exporter'); ?></label>
                                </th>
                                <td>
                                    <input type="checkbox" name="include_acf_taxonomies" id="include_acf_taxonomies" value="1" checked>
                                    <label for="include_acf_taxonomies"><?php _e('Export ACF custom taxonomies and terms', 'wc-acf-product-exporter'); ?></label>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="batch_size"><?php _e('Batch Size', 'wc-acf-product-exporter'); ?></label>
                                </th>
                                <td>
                                    <input type="number" name="batch_size" id="batch_size" value="50" min="10" max="500">
                                    <p class="description"><?php _e('Number of products to process at once. Lower values use less memory.', 'wc-acf-product-exporter'); ?></p>
                                </td>
                            </tr>
                        </table>
                        
                        <p class="submit">
                            <button type="submit" class="button button-primary" id="start-export">
                                <?php _e('Start Export', 'wc-acf-product-exporter'); ?>
                            </button>
                            <button type="button" class="button button-secondary" id="test-export" style="margin-left: 10px;">
                                <?php _e('Test Export', 'wc-acf-product-exporter'); ?>
                            </button>
                        </p>
                    </form>
                </div>
                
                <div class="wc-acf-export-progress" id="export-progress" style="display: none;">
                    <h3><?php _e('Export Progress', 'wc-acf-product-exporter'); ?></h3>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <p class="progress-text" id="progress-text"></p>
                    <div class="export-log" id="export-log"></div>
                </div>
                
                <div class="wc-acf-export-complete" id="export-complete" style="display: none;">
                    <h3><?php _e('Export Complete', 'wc-acf-product-exporter'); ?></h3>
                    <p><?php _e('Your product export has been completed successfully.', 'wc-acf-product-exporter'); ?></p>
                    <div class="export-downloads">
                        <a href="#" class="button button-primary" id="download-export">
                            <?php _e('Download CSV File', 'wc-acf-product-exporter'); ?>
                        </a>
                        <a href="#" class="button button-secondary" id="download-instructions" style="margin-left: 10px;">
                            <?php _e('Download Import Instructions', 'wc-acf-product-exporter'); ?>
                        </a>
                    </div>
                    <div class="import-info" style="margin-top: 15px; padding: 15px; background: #f0f8ff; border-left: 4px solid #0073aa;">
                        <h4><?php _e('Import Instructions', 'wc-acf-product-exporter'); ?></h4>
                        <p><?php _e('Before importing to another site:', 'wc-acf-product-exporter'); ?></p>
                        <ul>
                            <li><?php _e('Install WooCommerce and Advanced Custom Fields Pro', 'wc-acf-product-exporter'); ?></li>
                            <li><?php _e('Recreate all ACF field groups', 'wc-acf-product-exporter'); ?></li>
                            <li><?php _e('Create any custom taxonomies', 'wc-acf-product-exporter'); ?></li>
                            <li><?php _e('Download the import instructions file for detailed steps', 'wc-acf-product-exporter'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
}
